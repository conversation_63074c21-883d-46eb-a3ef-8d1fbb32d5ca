# Copyright 2024 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

name: pay_platform_interface
description: A common platform interface for payment plugins for Flutter.
version: 1.0.4
homepage: https://github.com/google-pay/flutter-plugin

environment:
  sdk: ">=3.0.0 <4.0.0"
  flutter: ">=3.10.0"

flutter:
  assets:
    - pubspec.yaml

dependencies:
  flutter:
    sdk: flutter
  yaml: ^3.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
