import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:quickalert/quickalert.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:waie_app/models/branch.dart';
import 'package:waie_app/models/department.dart';
import 'package:waie_app/models/division.dart';
import 'package:waie_app/models/fleet.dart';
import 'package:waie_app/models/fleet_structure.dart';
import 'package:waie_app/models/loadplaces.dart';
import 'package:waie_app/models/operation.dart';
import 'package:waie_app/utils/api_endpoints.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/dashboard_manager/dashboard_manager.dart';
import 'package:waie_app/view/screen/vehicles_screen/my_fleet/final_view_vehicle_details_screen.dart';
import 'package:waie_app/view/widget/common_button.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';

import '../../../../models/load_data.dart';
import '../../../../models/newfleet.dart';
import '../../../../utils/constants.dart';
import '../../../../view/screen/vehicles_screen/my_fleet/final_edit_vehicle_details_screen.dart';
import '../../../../view/widget/loading_widget.dart';

class AddDigitalCouponVehicleDetailsController extends GetxController {
  @override
  void onInit() {
    super.onInit();
    print('Controller created and dependencies injected');

    getVehicleDetails();
    loadDivision();
    loadStationsForSelectedOption();
    // Select all days at startup
    tag.value = List.generate(daysList.length, (index) => index);
    isAllDays.value = true;
    tags.value = daysList.join(",");
    customerData = custsData.read('custData');
  }

  var customerData;

  RxString tagList = "".obs;
  List<String> tagLists = [];
  // List<String> daysList = [
  //   "Monday",
  //   "Tuesday",
  //   "Wednesday",
  //   "Thursday",
  //   "Friday",
  //   "Saturday",
  //   "Sunday",
  // ];

  final List<String> daysList = [
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
    "Sunday",
  ];

  RxList<int> tag = <int>[].obs;

  RxBool isAllDays = false.obs;

  RxString tags = "".obs;

  void onSelectAllDays(bool value) {
    if (value) {
      tag.value = List.generate(daysList.length, (i) => i);
    } else {
      tag.clear();
    }
    isAllDays.value = value;
    tags.value = value ? daysList.join(",") : "";
  }

  void onDaysChanged(List<int> values) {
    tag.value = values;
    isAllDays.value = (tag.length == daysList.length);
    tags.value = tag.map((i) => daysList[i]).join(",");
  }

  RxBool isAvailableStations = true.obs;
  RxList availableStationList = [
    {"value": true.obs, "title": "WAIE stations"}.obs,
    {"value": false.obs, "title": "Non-WAIE stations"}.obs,
    {"value": false.obs, "title": "Sub-contractors"}.obs,
  ].obs;

  RxString selectedOption = 'WAIE Stations'.obs; // example

  final options = ['WAIE Stations', 'Non WAIE Stations', 'Subcontractors'];
  RxBool isRefuelLimits = true.obs;
  RxBool isVehicleDetail = true.obs;

  RxBool sticker = false.obs;
  RxBool isLoading = true.obs;
  RxBool isOrganization = true.obs;
  RxBool isFillingDays = true.obs;
  final countryList = [
    {
      "TYPEID": "COUNTRY",
      "TYPECODE": "KSA",
      "TYPEDESC": "KSA",
      "ORDERNO": 1.0,
    },
    {
      "TYPEID": "COUNTRY",
      "TYPECODE": "UAE",
      "TYPEDESC": "UAE",
      "ORDERNO": 2.0,
    },
    {
      "TYPEID": "COUNTRY",
      "TYPECODE": "KUW",
      "TYPEDESC": "KUWAIT",
      "ORDERNO": 3.0,
    },
    {
      "TYPEID": "COUNTRY",
      "TYPECODE": "BAH",
      "TYPEDESC": "BAHRAIN",
      "ORDERNO": 4.0,
    },
    {
      "TYPEID": "COUNTRY",
      "TYPECODE": "OMA",
      "TYPEDESC": "OMAN",
      "ORDERNO": 5.0,
    },
    {
      "TYPEID": "COUNTRY",
      "TYPECODE": "QAT",
      "TYPEDESC": "QATAR",
      "ORDERNO": 6.0,
    }
  ];
  final vehlicTypeList = [
    {
      "TYPEID": "VEHLIC_TYPE",
      "TYPECODE": "PR",
      "TYPEDESC": "PRIVATE",
      "ORDERNO": 1.0,
    },
    {
      "TYPEID": "VEHLIC_TYPE",
      "TYPECODE": "PRT",
      "TYPEDESC": "PRIVATE TRANSPORT",
      "ORDERNO": 2.0,
    },
    {
      "TYPEID": "VEHLIC_TYPE",
      "TYPECODE": "PUT",
      "TYPEDESC": "PUBLIC TRANSPORT",
      "ORDERNO": 3.0,
    },
    {
      "TYPEID": "VEHLIC_TYPE",
      "TYPECODE": "PUB",
      "TYPEDESC": "PUBLIC BUS",
      "ORDERNO": 4.0,
    },
    {
      "TYPEID": "VEHLIC_TYPE",
      "TYPECODE": "PRB",
      "TYPEDESC": "PRIVATE BUS",
      "ORDERNO": 5.0,
    },
    {
      "TYPEID": "VEHLIC_TYPE",
      "TYPECODE": "TX",
      "TYPEDESC": "TAXI",
      "ORDERNO": 6.0,
    },
    {
      "TYPEID": "VEHLIC_TYPE",
      "TYPECODE": "DP",
      "TYPEDESC": "DIPLOMATIC",
      "ORDERNO": 7.0,
    },
    {
      "TYPEID": "VEHLIC_TYPE",
      "TYPECODE": "PW",
      "TYPEDESC": "PUBLIC WORKS",
      "ORDERNO": 8.0,
    }
  ];

  final vehicleTypeList = [
    {
      "TYPEID": "VEHLIC_TYPE",
      "TYPECODE": "PR",
      "TYPEDESC": "CAR", //"C"
      "VEHICLETYPE": "C",
      "ORDERNO": 1.0,
    },
    {
      "TYPEID": "VEHLIC_TYPE",
      "TYPECODE": "PRT",
      "TYPEDESC": "TRUCK", //"T"
      "VEHICLETYPE": "T",
      "ORDERNO": 2.0,
    },
    {
      "TYPEID": "VEHLIC_TYPE",
      "TYPECODE": "PUT",
      "TYPEDESC": "PICKUP", //P
      "VEHICLETYPE": "P",
      "ORDERNO": 3.0,
    },
    {
      "TYPEID": "VEHLIC_TYPE",
      "TYPECODE": "PUB",
      "TYPEDESC": "VAN", //V
      "VEHICLETYPE": "V",
      "ORDERNO": 4.0,
    },
    {
      "TYPEID": "VEHLIC_TYPE",
      "TYPECODE": "PRB",
      "TYPEDESC": "SUV", //S
      "VEHICLETYPE": "S",
      "ORDERNO": 5.0,
    },
    {
      "TYPEID": "VEHLIC_TYPE",
      "TYPECODE": "TX",
      "TYPEDESC": "MOTORCYCLE", //M
      "VEHICLETYPE": "M",
      "ORDERNO": 6.0,
    },
    {
      "TYPEID": "VEHLIC_TYPE",
      "TYPECODE": "DP",
      "TYPEDESC": "MINIVAN", //N
      "VEHICLETYPE": "N",
      "ORDERNO": 7.0,
    },
    {
      "TYPEID": "VEHLIC_TYPE",
      "TYPECODE": "PW",
      "TYPEDESC": "BUS", //B
      "VEHICLETYPE": "B",
      "ORDERNO": 8.0,
    },
    {
      "TYPEID": "VEHLIC_TYPE",
      "TYPECODE": "PW",
      "TYPEDESC": "TRAILER", //R
      "VEHICLETYPE": "R",
      "ORDERNO": 8.0,
    },
    {
      "TYPEID": "VEHLIC_TYPE",
      "TYPECODE": "PW",
      "TYPEDESC": "FOUR-WHEEL DRIVE", //F
      "VEHICLETYPE": "F",
      "ORDERNO": 8.0,
    },
    {
      "TYPEID": "VEHLIC_TYPE",
      "TYPECODE": "PW",
      "TYPEDESC": "JEEP", //J
      "VEHICLETYPE": "J",
      "ORDERNO": 8.0,
    },
  ];

  final fuelList = [
    {
      "TYPEID": "FUELTYPE",
      "TYPECODE": "PESE0",
      "TYPEDESC": "PETROL95",
      "ORDERNO": 1.0,
    },
    {
      "TYPEID": "FUELTYPE",
      "TYPECODE": "PE910",
      "TYPEDESC": "PETROL91",
      "ORDERNO": 2.0,
    },
    {
      "TYPEID": "FUELTYPE",
      "TYPECODE": "DISE0",
      "TYPEDESC": "DIESEL",
      "ORDERNO": 3.0,
    }
  ];

  final quotaTypeList = [
    {
      "TYPEID": "QUOTYPE",
      "TYPECODE": "158",
      "TYPEDESC": "DAILY",
      "ORDERNO": 2.0,
    },
    {
      "TYPEID": "QUOTYPE",
      "TYPECODE": "157",
      "TYPEDESC": "UNLIMITED",
      "ORDERNO": 4.0,
    },
    {
      "TYPEID": "QUOTYPE",
      "TYPECODE": "160",
      "TYPEDESC": "MONTHLY",
      "ORDERNO": 5.0,
    },
    {
      "TYPEID": "QUOTYPE",
      "TYPECODE": "159",
      "TYPEDESC": "WEEKLY",
      "ORDERNO": 9.0,
    }
  ];
  GetStorage custsData = GetStorage('custsData');
  GetStorage userStorage = GetStorage('User');
  //List<LoadPlaces> loadPlaces = [];

  RxList placeList = [].obs;
  RxBool chckbox = false.obs;
  RxBool isOffline = false.obs;
  final countryListt = <Load_Data_Model>[].obs;
  final vehlicTypeListt = <Load_Data_Model>[].obs;
  final vehicleTypeListt = <Load_Data_Model>[].obs;
  final fuelListt = <Load_Data_Model>[].obs;
  final quotaTypeListt = <Load_Data_Model>[].obs;
  final quotaClassList = <Load_Data_Model>[].obs;
  final List<ServiceObj> vehicleDetails = [];
  List<Load_Data_Model> countryModelList = [];
  List<Load_Data_Model> vehlicTypeModelList = [];
  List<Load_Data_Model> vehicleTypeModelList = [];
  List<Load_Data_Model> fuelModelList = [];
  List<Load_Data_Model> quotaTypeModelList = [];
  List<Load_Data_Model> quotaClassModelList = [];
  String country = "KSA";
  RxString vehlicType = "".obs;
  RxString plateNo = "".obs;
  RxString driverName = "".obs;
  RxString vehicleType = "".obs;
  RxString fuelType = "".obs;
  RxString selectedDivsion = "".obs;
  RxString selectedBranch = "".obs;
  RxString selectedDepartment = "".obs;
  RxString selectedOperation = "".obs;
  RxString tanks = "".obs;
  RxDouble offLine = 0.00.obs;
  RxString password = "".obs;
  RxString vehicleReference = "".obs;
  RxString quotaType = "".obs;
  RxString quotaClass = "".obs;
  RxString quotaValue = "".obs;
  RxString fillingDays = "".obs;
  RxString serviceStatus = "".obs;
  RxString serviceType = "".obs;
  RxString isPlateNo = "".obs;
  RxString isSerialID = "".obs;

  RxString coupType = "".obs;
  RxString dcMobileNo = "".obs;
  RxString isCoup = "".obs;
  RxString isDC = "".obs;
  RxString isServiceType = "".obs;

  RxBool isDCActive = false.obs;
  //RxString dcDocNo = "".obs;
  //RxString dcQR = "".obs;

  TextEditingController plateNoController = TextEditingController();
  TextEditingController driverNameController = TextEditingController();
  TextEditingController offlineController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  TextEditingController vehicleReferenceController = TextEditingController();
  TextEditingController quotaValController = TextEditingController();
  TextEditingController quotaBalController = TextEditingController();
  TextEditingController insTermDateController = TextEditingController();
  TextEditingController serialXIDController = TextEditingController();
  TextEditingController countryController = TextEditingController();
  TextEditingController dcQuotaRemController = TextEditingController();
  TextEditingController dcFuelQuotaController = TextEditingController();
  TextEditingController driverMobileNoController = TextEditingController();

  loadVehicleDetails() {}

  getVehicleDetails() async {
    // print("serialid>>>>>>> $serialid");
    // Loader.showLoader();
    var client = http.Client();
    List<ServiceObj> details = [];
    var custData = custsData.read('custData');
    print("custid>>>>>>> $custData['CUSTID']");
    print("ViewVehicleDetailsController>>>>>>> getVehicleDetails");
    //print("emailid>>>>>>> $custData['EMAILID']");
    var custid = userStorage.read('custid');
    var emailid = userStorage.read('emailid');
    print("custid>>>>>>> $custid");
    print("emailid>>>>>>> $emailid");

    try {
      String username = 'aldrees';
      String password = 'testpass';
      String basicAuth =
          'Basic ${base64Encode(utf8.encode('$username:$password'))}';
      Map body = {
        "SEARCHBY": "",
        "TOP": "1",
        "SKIP": "0",
        "CUSTID": custid, //"000003944",
        //"SERIALID": serialid,
        "UNAME": custData['USERNAME']
      };
      var vehicleResponse = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getFleets),
          body: jsonEncode(body),
          headers: {
            'authorization': basicAuth,
            "Content-Type": "application/json",
          });

      //List vehicleResult = jsonDecode(vehicleResponse.body);

      var countryResponse = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getAULookups),
          body: {
            "IsAR": Constants.IsAr_App,
            "TypeId": "COUNTRY",
            "CustId": custData["CUSTID"],
          });

      //log("country response ${countryResponse.body}");

      var vehlicTypeResponse = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getAULookups),
          body: {
            "IsAR": Constants.IsAr_App,
            "TypeId": "VEHLIC_TYPE",
            "CustId": custData["CUSTID"],
          });

      log("vehlicTypeResponse response ${vehlicTypeResponse.body}");

      var vehicleTypeResponse = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getAULookups),
          body: {
            "IsAR": Constants.IsAr_App,
            "TypeId": "VEHTYPE",
            "CustId": custData["CUSTID"],
          });

      log("vehicleTypeResponse response 2nd ${vehlicTypeResponse.body}");

      var fuelResponse = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getAULookups),
          body: {
            "IsAR": Constants.IsAr_App,
            "TypeId": "FUELTYPE",
            "CustId": custData["CUSTID"],
          });
      // log("fuelResponse ${fuelResponse.body}");

      var quotaTypeResponse = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getAULookups),
          body: {
            "IsAR": Constants.IsAr_App,
            "TypeId": "QUOTYPE",
            "CustId": custData["CUSTID"],
          });

      // log("quotaTypeResponse ${quotaTypeResponse.body}");

      var quotaClassResponse = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getAULookups),
          body: {
            "IsAR": Constants.IsAr_App,
            "TypeId": "QUOCLASS",
            "CustId": custData["CUSTID"],
          });

      var wResponse = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.loadPlaces),
//          body: {"userId": "000037345","ACCTTYPE" : "C"});
          body: {
            "CUSTID": custid,
            "OTHERSTN": custData['OTHERSTN'],
            "StationType": "W",
            "fuelType": "PE910",
            "IsAR": Constants.IsAr_App,
          });

      var isDCACTIVE = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.getDCStatus),
          body: {
            "CustId": custData["CUSTID"],
          });

      print("isDCACTIVE>>>>>>> ${jsonDecode(isDCACTIVE.body)}");

      final vehicleResult = json.decode(vehicleResponse.body);
      inspect(vehicleResult);

      List wResult = jsonDecode(wResponse.body);

      print("============================================");
      print("vehicleResult");
      print(json.decode(vehicleResponse.body));
      print("============================================");
      print(vehicleResult);
      print("============================================");

      final dataList = vehicleResult["serviceObj"] as List<dynamic>;

      final List<ServiceObj> newData =
          dataList.map((item) => ServiceObj.fromJson(item)).toList();

      vehicleDetails.addAll(newData);

      if (jsonDecode(isDCACTIVE.body) == true) {
        print(" YES YES YES YES isDCACTIVE");
      }

      isDCActive.value =
          vehicleDetails[0].isdc == "Y" ? true : jsonDecode(isDCACTIVE.body);

      print("isDCACTIVE>>>>>>> $isDCActive");

      print("isServiceType>>>>>>> ${vehicleDetails[0].servicetype}");
      print("isDCACTIVE>>>>>>> ${vehicleDetails[0].isdc}");
      print("dcQuotarem>>>>>>> ${vehicleDetails[0].dcQuotarem}");
      print("dcQuotavalue>>>>>>> ${vehicleDetails[0].dcQuotavalue}");
      print("DOCNO>>>>>>> ${vehicleDetails[0].dcDocno}");
      print("SERIAL>>>>>>> ${vehicleDetails[0].serialid}");

      country = vehicleDetails[0].country;
      isServiceType.value = vehicleDetails[0].servicetype;
      isCoup.value = vehicleDetails[0].couptype;
      isDC.value = vehicleDetails[0].isdc;
      dcQuotaRemController.text = vehicleDetails[0].dcQuotarem.toString();
      dcFuelQuotaController.text = vehicleDetails[0].dcQuotavalue.toString();
      vehlicType.value = vehicleDetails[0].vehlicType;
      plateNoController.text =
          vehicleDetails[0].plateno == vehicleDetails[0].serialid
              ? ""
              : vehicleDetails[0].plateno;
      driverNameController.text = vehicleDetails[0].driver;
      vehicleType.value = vehicleDetails[0].vehicletype;
      fuelType.value = vehicleDetails[0].fueltype;
      offlineController.text =
          vehicleDetails[0].offlinelimit.toDouble().toString();
      quotaType.value = vehicleDetails[0].quotatype;
      quotaClass.value = vehicleDetails[0].quotaclass;
      quotaValController.text =
          vehicleDetails[0].quotavalue.toDouble().toString();
      quotaBalController.text =
          vehicleDetails[0].quotabal.toDouble().toString();
      serviceStatus.value = vehicleDetails[0].servicestatus;
      serviceType.value = vehicleDetails[0].servicetype;
      vehicleReferenceController.text =
          vehicleDetails[0].uniquefield.toString();
      passwordController.text = vehicleDetails[0].servicepw.toString();
      insTermDateController.text = vehicleDetails[0].servicestatus == "T"
          ? vehicleDetails[0].terminatedate
          : vehicleDetails[0].instdate;
      serialXIDController.text = vehicleDetails[0].servicetype == "C"
          ? vehicleDetails[0].serialid
          : hidePartOfNumber(vehicleDetails[0].serialcode);
      // fillingDays.value = vehicleDetails[0].
      isSerialID.value = vehicleDetails[0].serialid;
      isPlateNo.value = vehicleDetails[0].plateno;
      //dcDocNo.value = vehicleDetails[0].dcDocno!;
      //dcQR.value = vehicleDetails[0].dcQr!;

      print("vehicleDetails===111111> ${vehicleDetails[0].vehlicType}");

      print("countryResponse===> ${jsonDecode(countryResponse.body)}");

      List countryResult = jsonDecode(countryResponse.body);

      for (int i = 0; i < countryResult.length; i++) {
        Load_Data_Model loadData =
            Load_Data_Model.fromMap(countryResult[i] as Map<String, dynamic>);
        countryModelList.add(loadData);
        print("countryResponse ===============${loadData.TYPEDESC}");
      }

      countryListt.value = countryModelList;

      print("vehlicTypeResponse===> ${jsonDecode(vehlicTypeResponse.body)}");

      List vehlicTypeResult = jsonDecode(vehlicTypeResponse.body);

      for (int i = 0; i < vehlicTypeResult.length; i++) {
        Load_Data_Model loadData1 = Load_Data_Model.fromMap(
            vehlicTypeResult[i] as Map<String, dynamic>);
        vehlicTypeModelList.add(loadData1);
        print("vehlicTypeResponse ===============${loadData1.TYPEDESC}");
      }

      vehlicTypeListt.value = vehlicTypeModelList;

      //print("vehicleTypeResponse===> ${jsonDecode(vehicleTypeResponse.body)}");

      List vehicleTypeResult = jsonDecode(vehicleTypeResponse.body);

      for (int i = 0; i < vehicleTypeResult.length; i++) {
        Load_Data_Model loadData2 = Load_Data_Model.fromMap(
            vehicleTypeResult[i] as Map<String, dynamic>);
        vehicleTypeModelList.add(loadData2);
        print("vehicleTypeResponse ===============${loadData2.TYPEDESC}");
      }

      vehlicTypeListt.value = vehicleTypeModelList;

      log("fuelResponse===> ${jsonDecode(fuelResponse.body)}");

      List fuelResult = jsonDecode(fuelResponse.body);

      for (int i = 0; i < fuelResult.length; i++) {
        Load_Data_Model loadData3 =
            Load_Data_Model.fromMap(fuelResult[i] as Map<String, dynamic>);
        fuelModelList.add(loadData3);
        print("fuelResponse ===============${loadData3.TYPEDESC}");
      }

      fuelListt.value = fuelModelList;

      print("quotaTypeResponse===> ${jsonDecode(quotaTypeResponse.body)}");

      List quotaTypeResult = jsonDecode(quotaTypeResponse.body);

      for (int i = 0; i < quotaTypeResult.length; i++) {
        Load_Data_Model loadData4 =
            Load_Data_Model.fromMap(quotaTypeResult[i] as Map<String, dynamic>);
        quotaTypeModelList.add(loadData4);
        print("quotaTypeResponse ===============${loadData4.TYPEDESC}");
      }

      quotaTypeListt.value = quotaTypeModelList;

      print("quotaClassResponse===> ${jsonDecode(quotaClassResponse.body)}");

      List quotaClassResult = jsonDecode(quotaClassResponse.body);

      for (int i = 0; i < quotaClassResult.length; i++) {
        Load_Data_Model loadData5 = Load_Data_Model.fromMap(
            quotaClassResult[i] as Map<String, dynamic>);
        quotaClassModelList.add(loadData5);
        print("quotaClassResponse ===============${loadData5.TYPEDESC}");
      }

      quotaClassList.value = quotaClassModelList;

      // for (int i = 0; i < vehicleResult.length; i++) {
      //   print("i===> $i --- ${vehicleResult.length}");
      // }

      // for (int i = 0; i < vehicleResult.length; i++) {
      //   try {
      //     FleetModel detail =
      //         FleetModel.fromMap(vehicleResult[i] as Map<String, dynamic>);
      //     details.add(detail);
      //   } catch (e) {
      //     print("e.toString()===> ${e.toString()}");
      //   }
      // }

      for (int i = 0; i < wResult.length; i++) {
        LoadPlaces place =
            LoadPlaces.fromJson(wResult[i] as Map<String, dynamic>);
        // loadPlaces.add(place);
      }

      print("response vehicleDetails===> ${vehicleDetails.length}");

      if (wResponse.statusCode == 200) {
        Loader.hideLoader();
        await Get.to(() => const FinalEditVehicleDetailsScreen());
      } else {
        Loader.hideLoader();
        print('Failed');
        showDialog(
          barrierDismissible: false,
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(24),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    "Failed to Load Vehicle Details",
                    style: pBold20,
                    textAlign: TextAlign.center,
                  ),
                  verticalSpace(24),
                  CommonButton(
                    title: "OK",
                    onPressed: () async {
                      Get.back();
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                ],
              ),
            );
          },
        );
      }

      return vehicleDetails;
    } catch (e) {
      log(e.toString());
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  hidePartOfNumber(String rfid) {
    // Convert the number to a string
    String numberString = rfid.toString();

    // Determine how many digits to hide (in this example, hiding all but the last four digits)
    int digitsToShow = 6;
    int visibleDigits = numberString.length - digitsToShow;

    // Replace the visible digits with asterisks
    String hiddenDigits = 'X' * visibleDigits;

    // Concatenate the visible part of the number with the hidden part
    String maskedNumber = hiddenDigits + numberString.substring(visibleDigits);

    return maskedNumber;
  }

  RxList divisionList = <DivisionModel>[].obs;
  loadDivision() async {
    // clearSelectionsBelow('division');

    print("loadDivision");
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.loadDivision),
          body: {
            "custid": userid,
          });

      log("loadDivision(response.body) .>>>>> ${jsonDecode(response.body)}");
      List result = jsonDecode(response.body);

      if (response.statusCode == 200) {
        for (int i = 0; i < result.length; i++) {
          DivisionModel structures =
              DivisionModel.fromJson(result[i] as Map<String, dynamic>);
          divisionList.add(structures);
        }
        log("access divisionList ${divisionList.first.typecode}");
        //loadBranch(divisionList.first.typecode);
        return divisionList;
      }
    } catch (e) {
      log(e.toString());
    } finally {
      client.close();
    }
  }

  RxList branchList = <BranchModel>[].obs;

  loadBranch(parentID) async {
    print("loadBranch");
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    log("parent id ${parentID}");
    log("userid id ${userid}");

    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.loadBranch),
          body: {
            "custid": userid,
            "parentid": parentID,
          });

      log("loadBranch(response.body) .>>>>> ${jsonDecode(response.body)}");

      if (response.statusCode == 200) {
        List result = jsonDecode(response.body);

        for (int i = 0; i < result.length; i++) {
          BranchModel structures =
              BranchModel.fromJson(result[i] as Map<String, dynamic>);
          branchList.add(structures);
        }
        log("access branchList ${branchList.first.typecode}");

        //loadDepartment(branchList.first.typecode);
      }

      return branchList;
    } catch (e) {
      log(e.toString());
    } finally {
      client.close();
    }
  }

  final departmentList = <DepartmentModel>[].obs;

  Future<dynamic> loadDepartment(parentID) async {
    print("loadDepartment");
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.loadDepartment),
          body: {
            "custid": userid,
            "parentid": parentID,
          });

      log("loadDepartment(response.body) .>>>>> ${jsonDecode(response.body)}");
      List result = jsonDecode(response.body);
      if (response.statusCode == 200) {
        for (int i = 0; i < result.length; i++) {
          DepartmentModel structures =
              DepartmentModel.fromJson(result[i] as Map<String, dynamic>);
          departmentList.add(structures);
        }

        log("access departmentList ${departmentList.first.typecode}");

        // loadOperation(departmentList.first.typecode);
      }

      return departmentList;
    } catch (e) {
      log(e.toString());
    } finally {
      client.close();
    }
  }

  RxList operationList = <OperationModel>[].obs;

  loadOperation(parentID) async {
    var client = http.Client();
    SharedPreferences sharedUser2 = await SharedPreferences.getInstance();
    var userid = sharedUser2.getString('userid');
    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.loadOperation),
          body: {
            "custid": userid,
            "parentid": parentID,
          });

      log("loadOperation(response.body) .>>>>> ${jsonDecode(response.body)}");

      if (response.statusCode == 200) {
        List result = jsonDecode(response.body);
        for (int i = 0; i < result.length; i++) {
          OperationModel structures =
              OperationModel.fromJson(result[i] as Map<String, dynamic>);
          operationList.add(structures);
        }
      }

      return operationList;
    } catch (e) {
      log(e.toString());
    } finally {
      client.close();
    }
  }

  clearSelectionsBelow(String level) {
    switch (level) {
      case 'division':
        selectedBranch.value = "";
        branchList.clear();
        selectedDepartment.value = "";
        departmentList.clear();
        selectedOperation.value = "";
        operationList.clear();
        break;
      case 'branch':
        selectedDepartment.value = "";
        departmentList.clear();
        selectedOperation.value = "";
        operationList.clear();
        break;
      case 'department':
        selectedOperation.value = "";
        operationList.clear();
        break;
    }
  }

  RxList<Map<String, dynamic>> stationList = <Map<String, dynamic>>[].obs;

  getAvailableStations({required String stationType}) async {
    var custData = custsData.read('custData');
    print("custid>>>>>>> $custData['CUSTID']");
    print("emailid>>>>>>> $custData['EMAILID']");
    var client = http.Client();
    try {
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.loadPlaces),
          body: {
            "CUSTID": custData['CUSTID'],
            "OTHERSTN": custData['OTHERSTN'],
            "StationType": stationType,
            "fuelType": "",
            "IsAR": Constants.IsAr_App,
          });
      List result = jsonDecode(response.body);
      // log("result $result Api");
      //updateStationListFromApi(result);
      updateStationListFromApi(result);
      // for (int i = 0; i < result.length; i++) {
      //   LoadPlaces place =
      //       LoadPlaces.fromJson(result[i] as Map<String, dynamic>);
      //   loadPlaces.add(place);
      // }

      return result;
    } catch (e) {
      log(e.toString());
      return [];
    } finally {
      client.close();
    }
  }

  void updateStationListFromApi(dynamic apiData) {
    stationList.clear();
    for (var place in apiData) {
      var stations = place['Stations'];
      if (stations == null || stations is! List) stations = <dynamic>[];

      stationList.add({
        "title": place['PlaceName']?.trim() ?? 'Unknown Place',
        "placeCode": place['PlaceCode'] ?? "",
        "expanded": false.obs,
        "value": false.obs,
        "data": stations.map((station) {
          return {
            "title": station['StationName'] ?? 'Unknown Station',
            "stationNo": station['StationNo'] ?? "",
            "value": false.obs,
          };
        }).toList(),
      });
    }
    toggleAll(true);
  }

  String get stationTypeCode {
    switch (selectedOption.value) {
      case 'WAIE Stations':
        return 'W';
      case 'Non WAIE Stations':
        return 'D';
      case 'Subcontractors':
        return 'S';
      default:
        return 'W';
    }
  }

  Future<void> loadStationsForSelectedOption() async {
    log('loading stations for option: $stationTypeCode');
    isLoading.value = true;
    try {
      var apiData = await getAvailableStations(stationType: stationTypeCode);
      print('API data count: ${apiData.length}');
      updateStationListFromApi(apiData);
      print('stationList count after update: ${stationList.length}');
    } catch (e) {
      stationList.clear();
      print('Error loading stations: $e');
    } finally {
      isLoading.value = false;
    }
  }

  RxBool allSelected = false.obs;

  bool get isAllChecked => stationList.every((city) => (city['data'] as List)
      .every((station) => (station['value'] as RxBool).value));

  bool get isIndeterminate =>
      !isAllChecked &&
      stationList.any((city) => (city['data'] as List)
          .any((station) => (station['value'] as RxBool).value));

  void toggleAll(bool value) {
    for (var city in stationList) {
      (city['value'] as RxBool).value = value;
      for (var station in city['data']) {
        (station['value'] as RxBool).value = value;
      }
    }
  }

  void toggleExpansion(int index) {
    (stationList[index]['expanded'] as RxBool).value =
        !(stationList[index]['expanded'] as RxBool).value;
  }

  bool? getCityCheckboxValue(int cityIdx) {
    if (cityIdx < 0 || cityIdx >= stationList.length) return false;
    final stations = stationList[cityIdx]['data'] as List;
    final allSelected =
        stations.every((station) => (station['value'] as RxBool).value);
    final anySelected =
        stations.any((station) => (station['value'] as RxBool).value);

    if (allSelected) return true;
    if (anySelected) return null;
    return false;
  }

  void toggleCity(int index, bool value) {
    (stationList[index]['value'] as RxBool).value = value;
    for (var station in stationList[index]['data']) {
      (station['value'] as RxBool).value = value;
    }
  }

  void toggleStation(int cityIdx, int stationIdx, bool value) {
    (stationList[cityIdx]['data'][stationIdx]['value'] as RxBool).value = value;
    final city = stationList[cityIdx];
    final cityStations = city['data'] as List;
    final allSelected =
        cityStations.every((st) => (st['value'] as RxBool).value);
    (city['value'] as RxBool).value = allSelected;
  }

  Future<void> saveSelectedCitiesStations() async {
    List<Map<String, dynamic>> payload = [];

    for (var city in stationList) {
      final selectedStations = (city['data'] as List)
          .where((station) => (station['value'] as RxBool).value)
          .map((station) => station['stationNo'])
          .where((sn) => sn != null && sn != "") // Filter out any blanks
          .toList();

      if (selectedStations.isNotEmpty) {
        payload.add({
          "PlaceCode": city['placeCode'], // <-- Will now be filled
          "Stations": selectedStations, // <-- Actual station numbers
        });
      }
    }

    log("selected stations to send API $payload");
  }

  void addFleet({
    required String custId,
    required String custUserName,
    required String country,
    required String vehlicType,
    required String plateNo,
    required String vehicleType,
    required String fuelType,
    required String driverName,
    required String password,
    required String refName,
    required String divisionCode,
    required String divisionName,
    required String branchCode,
    required String branchName,
    required String departmentCode,
    required String departmentName,
    required String operationCode,
    required String operationName,
    required String qoutaType,
    required String qoutaClass,
    required bool isSticker,
    required String mobileNumber,
    required String dcFuelQouta,
    required fillingDaysList,
    required waieStationsList,
    required nonWaieStationsList,
    required subContractorsStationsList,
  }) async {
    var client = http.Client();
    var body = {
      "CUSTID": custId,
      "CUSTUSERNAME": custUserName,
      "IsDC": "Y",
      "SERVICESTATUS": "",
      "PLATENO": plateNo,
      "VEHICLETYPE": vehicleType,
      "VEHLIC_TYPE": vehlicType,
      "DRIVER": driverName,
      "FUELTYPE": fuelType,
      "QUOTATYPE": qoutaType,
      "QUOTAVALUE": "",
      "QUOTABAL": "",
      "DC_QUOTAVALUE": quotaValue,
      "DC_QUOTAVALUE2": 0,
      "SERVICETYPE": "D",
      "PWFLAG": "Y",
      "SERVICEPW": password,
      "QUOTACLASS": "1",
      "ISOFFLINE": "0",
      "OFFLINELIMIT": "",
      "DIVISION": divisionCode,
      "DIVISIONNAME": divisionName,
      "BRANCH": branchCode,
      "BRANCHNAME": branchName,
      "DEPTNO": departmentCode,
      "DEPTNAME": departmentName,
      "OPERATION": operationCode,
      "OPERATIONNAME": operationName,
      "TANK_NO": "1",
      "TRANSFERDATE": "",
      "UNIQUEFIELD": refName,
      "INSTDATE": 1,
      "SERIALID": "",
      "SERIALCODE": "",
      "STATION_PUSH": "",
      "UNLI_VALUE": "",
      "NEWSTATIONS": "",
      "DELETEDSTATIONS": "",
      "PINBLOCK": "N",
      "DC_MOBILENO": mobileNumber,
      "TERMINATE": false,
      "COUNTRY": country,
      "COUPTYPE": (isSticker) ? "S" : "Q",
      "VEHICLEID": "",
      "FILLINGDAYS": fillingDaysList,
      "STATIONS": waieStationsList,
      "DC_STATIONS": nonWaieStationsList,
      "SUB_STATIONS": subContractorsStationsList,
      "AUSNF": []
    };

    print("Request Body ======== $body");

    try {
      String username = ApiEndPoints.username;
      String password = ApiEndPoints.password;
      String basicAuth =
          'Basic ${base64Encode(utf8.encode('$username:$password'))}';
      var response = await client.post(
          Uri.parse(ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.addFleet),
          body: jsonEncode(body),
          headers: {
            'authorization': basicAuth,
            "Content-Type": "application/json",
          });

      List result = jsonDecode(response.body);

      print("addFleets statusCode===> ${response.statusCode}");
      print(" addFleetsresponse body===> ${response.body}");

      print(" addFleets response ===> $result");
    } catch (e) {
      log(e.toString());
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }

  saveVehicle() async {
    Loader.showLoader();
    var client = http.Client();
    var custid = userStorage.read('custid');
    var emailid = userStorage.read('emailid');
    var custData = custsData.read('custData');
    print("custid>>>>>>> $custid");
    print("emailid>>>>>>> $emailid");
    print("custData>>>>>>> $custData");
    print("custid>>>>>>> ${custData['CUSTID']}");
    print("dcFuelQuotaController>>>>>>> $dcFuelQuotaController");
    print("coupType>>>>>>> $coupType");
    print("driverMobileNoController>>>>>>> $driverMobileNoController");
    print("isDC.value>>>>>>> ${isDC.value}");
    print("passwordController>>>>>>> ${passwordController.text}");

    try {
      // if (isDC.value == 'Y') {
      //   if (dcFuelQuotaController.text.toString() == "0" ||
      //       dcFuelQuotaController.text.toString() == "0.0") {
      //     print("isDC1>>>>>>> ${isDC.value}");
      //     Loader.hideLoader();
      //     await QuickAlert.show(
      //         context: Get.context!,
      //         type: QuickAlertType.error,
      //         text: "Please set amount of DC fuel quota");
      //   }
      // } else if (isDC.value == 'Y' && passwordController.text == "") {
      //   print("isDC2>>>>>>> ${isDC.value}");
      //   Loader.hideLoader();
      //   await QuickAlert.show(
      //       context: Get.context!,
      //       type: QuickAlertType.error,
      //       text: "Please set password");
      // } else if (isDC.value == 'N' ||
      //     isDC.value == "" && passwordController.text == "" ||
      //     passwordController.text.isEmpty) {
      //   print("isDC3>>>>>>> ${isDC.value}");
      //   Loader.hideLoader();
      //   await QuickAlert.show(
      //       context: Get.context!,
      //       type: QuickAlertType.error,
      //       text: "Password is Required");
      // } else if (isDC.value == 'N' ||
      //     isDC.value == "" && offlineController.text == "" ||
      //     offlineController.text == "0.00") {
      //   print("isDC4>>>>>>> ${isDC.value}");
      //   Loader.hideLoader();
      //   await QuickAlert.show(
      //       context: Get.context!,
      //       type: QuickAlertType.error,
      //       text: "Offline Limit per Fuel is Required");
      // } else {
      //   print("isDC6>>>>>>> ${isDC.value}");
      String username = 'aldrees';
      String password = 'testpass';
      String basicAuth =
          'Basic ${base64Encode(utf8.encode('$username:$password'))}';
      Map body = {
        "IsDC": coupType.value != "" ? "Y" : vehicleDetails[0].isdc,
        "SERVICESTATUS": serviceStatus.value == ""
            ? vehicleDetails[0].servicestatus.toString()
            : serviceStatus.value.toString(),
        "PLATENO": plateNoController.text.toString() == ""
            ? vehicleDetails[0].plateno == vehicleDetails[0].serialid
                ? ""
                : vehicleDetails[0].plateno
            : plateNoController.text.toString(),
        "VEHICLETYPE": vehicleType.value == ""
            ? vehicleDetails[0].vehicletype.toString()
            : vehicleType.value.toString(),
        "VEHLIC_TYPE": vehlicType.value == ""
            ? vehicleDetails[0].vehlicType.toString()
            : vehlicType.value.toString(),
        "DRIVER": driverNameController.text.toString(),
        "FUELTYPE": fuelType.value == ""
            ? vehicleDetails[0].fueltype.toString()
            : fuelType.value.toString(),
        "QUOTATYPE": quotaType.value == ""
            ? vehicleDetails[0].quotatype.toString()
            : quotaType.value.toString(),
        "QUOTAVALUE": quotaValController.text.toString(),
        "QUOTABAL": quotaBalController.text.toString(),
        "DC_QUOTAVALUE": dcFuelQuotaController.text
            .toString(), // vehicleDetails[0].dcQuotavalue.toString() ?? ""
        "DC_QUOTAVALUE2": vehicleDetails[0].dcQuotavalue2.toString() ?? "",
        "SERVICETYPE": serviceType.value == ""
            ? vehicleDetails[0].servicetype.toString()
            : serviceType.value.toString(),
        "PWFLAG": vehicleDetails[0].pwflag.toString() ?? "",
        "SERVICEPW": passwordController.text,
        "QUOTACLASS": quotaClass.value == ""
            ? vehicleDetails[0].quotaclass.toString()
            : quotaClass.value.toString(),
        "ISOFFLINE": isOffline.value.toString(),
        "OFFLINELIMIT": offlineController.text.toString() == "" ||
                offlineController.text.toString() == "0.00"
            ? vehicleDetails[0].offlinelimit.toDouble().toString()
            : offlineController.text.toString(),
        "DIVISION": vehicleDetails[0].division.toString() ?? "",
        "DIVISIONNAME": vehicleDetails[0].divisionDisp.toString() ?? "",
        "BRANCH": vehicleDetails[0].branch.toString() ?? "",
        "BRANCHNAME": vehicleDetails[0].branchname.toString() ?? "",
        "DEPTNO": vehicleDetails[0].deptno.toString() ?? "",
        "DEPTNAME": vehicleDetails[0].deptname.toString() ?? "",
        "OPERATION": vehicleDetails[0].operation.toString() ?? "",
        "OPERATIONNAME": vehicleDetails[0].operationname.toString() ?? "",
        "TANK_NO": tanks.value == ""
            ? vehicleDetails[0].tankNo.toString()
            : tanks.value.toString(),
        "TRANSFERDATE": vehicleDetails[0].transferdate.toString() ?? "",
        "UNIQUEFIELD": vehicleReferenceController.text == ""
            ? vehicleDetails[0].uniquefield.toString()
            : vehicleReferenceController.text,
        "INSTDATE": vehicleDetails[0].instdate.toString() ?? "",
        "SERIALID": vehicleDetails[0].serialid.toString() ?? "",
        "SERIALCODE": vehicleDetails[0].serialcode.toString() ?? "",
        "STATION_PUSH": vehicleDetails[0].stationPush.toString() ?? "N",
        "UNLI_VALUE": vehicleDetails[0].unliValue.toString() ?? "0.00",
        "NEWSTATIONS": vehicleDetails[0].stations.toString() ?? "",
        "DELETEDSTATIONS": "NONE",
        "PINBLOCK": vehicleDetails[0].pinblock.toString() ?? "N",
        "DC_MOBILENO": driverMobileNoController.text == ""
            ? vehicleDetails[0].dcMobileno.toString()
            : driverMobileNoController.text,
        "TERMINATE": "false",
        "FILLINGDAYS": vehicleDetails[0].fillingdays.toString(),
        "COUNTRY": country == ""
            ? vehicleDetails[0].country.toString() == ""
                ? "KSA"
                : "KSA"
            : country.toString(),
        "COUPTYPE": coupType.value == ""
            ? vehicleDetails[0].couptype.toString()
            : coupType.value.toString(),
        "VEHICLEID": vehicleDetails[0].vehicleid.toString() ?? "",
        "CUSTID": custData['CUSTID'],
        "USERNAME": custData['USERNAME'],
        //"DC_DOCNO": vehicleDetails[0].dcDocno,
        //"DC_QR": vehicleDetails[0].dcQr,
      };

      print("============================================");
      print("body");
      print(body);
      print("============================================");
      var response = await client.post(
          Uri.parse(
              ApiEndPoints.baseUrl + ApiEndPoints.authEndpoints.editVehicle),
          body: jsonEncode(body),
          headers: {
            'authorization': basicAuth,
            "Content-Type": "application/json",
          });

      print(jsonEncode(response.body));

      if (jsonDecode(response.body)["MessageType"] == "success") {
        Loader.hideLoader();
        print(jsonDecode(response.body)["Message"]);
        //getVehicleDetails(vehicleDetails[0].serialid.toString());
        // orderHistoryList.refresh();
        // getOrderHistory();
        // await QuickAlert.show(
        //   context: Get.context!,
        //   type: QuickAlertType.success,
        //   text: jsonDecode(response.body)['Message'].toString(),
        // );
        // await Get.offAll(() => PurchaseHistoryScreen());
        // Get.back();
        // Get.back();
        showDialog(
          barrierDismissible: false,
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              insetPadding: const EdgeInsets.all(16),
              contentPadding: const EdgeInsets.all(24),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    jsonDecode(response.body)['Message'].toString(),
                    style: pRegular14.copyWith(color: AppColor.cDarkGreyFont),
                    textAlign: TextAlign.center,
                  ),
                  verticalSpace(24),
                  CommonButton(
                    title: "OK",
                    onPressed: () async {
                      //getVehicleDetails(vehicleDetails[0].serialid.toString());
                      //Get.offAll(() => const RefundMenuScreen());
                      await Get.offAll(() => DashBoardManagerScreen(
                            currantIndex: Constants.TopUpBtn == 'Y' ? 2 : 1,
                          ));
                    },
                    btnColor: AppColor.themeOrangeColor,
                  )
                ],
              ),
            );
          },
        );
      } else {
        Loader.hideLoader();
        print(jsonDecode(response.body)["message"]);
        await QuickAlert.show(
          context: Get.context!,
          type: QuickAlertType.error,
          text: jsonDecode(response.body)['message'].toString(),
        );
        // orderHistoryList.refresh();
        // getOrderHistory();
        // await QuickAlert.show(
        //   context: Get.context!,
        //   type: QuickAlertType.error,
        //   text: jsonDecode(response.body)['Message'].toString(),
        // );
        // Get.back();
        // showDialog(
        //   barrierDismissible: false,
        //   context: Get.context!,
        //   builder: (context) {
        //     return AlertDialog(
        //       insetPadding: const EdgeInsets.all(16),
        //       contentPadding: const EdgeInsets.all(24),
        //       shape: RoundedRectangleBorder(
        //           borderRadius: BorderRadius.circular(12)),
        //       content: Column(
        //         mainAxisSize: MainAxisSize.min,
        //         children: [
        //           Text(
        //             jsonDecode(response.body)['Message'].toString(),
        //             style: pRegular14.copyWith(color: AppColor.cDarkGreyFont),
        //             textAlign: TextAlign.center,
        //           ),
        //           verticalSpace(24),
        //           CommonButton(
        //             title: "OK".tr,
        //             onPressed: () async {
        //               //Get.offAll(() => const RefundMenuScreen());
        //               orderHistoryList.refresh();
        //               getOrderHistory();
        //               //Get.offAll(() => const RefundMenuScreen());
        //               await Get.off(() => PurchaseHistoryScreen(),
        //                   preventDuplicates: true);
        //               Get.back();
        //               Get.back();
        //             },
        //             btnColor: AppColor.themeOrangeColor,
        //           )
        //         ],
        //       ),
        //     );
        //   },
        // );
        //}

        return null;
      }
    } catch (e) {
      log(e.toString());
      return [];
    } finally {
      // Then finally destroy the client.
      client.close();
    }
  }
}
