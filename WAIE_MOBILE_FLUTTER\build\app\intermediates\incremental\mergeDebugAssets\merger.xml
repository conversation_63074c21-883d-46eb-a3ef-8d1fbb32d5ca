<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":webview_flutter_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\WAIE FLUTTER GIT\WAIE_MOBILE_FLUTTER\build\webview_flutter_android\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":video_player_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\WAIE FLUTTER GIT\WAIE_MOBILE_FLUTTER\build\video_player_android\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":url_launcher_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\WAIE FLUTTER GIT\WAIE_MOBILE_FLUTTER\build\url_launcher_android\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":sqflite" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\WAIE FLUTTER GIT\WAIE_MOBILE_FLUTTER\build\sqflite\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":smart_auth" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\WAIE FLUTTER GIT\WAIE_MOBILE_FLUTTER\build\smart_auth\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":shared_preferences_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\WAIE FLUTTER GIT\WAIE_MOBILE_FLUTTER\build\shared_preferences_android\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":permission_handler_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\WAIE FLUTTER GIT\WAIE_MOBILE_FLUTTER\build\permission_handler_android\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":pay_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\WAIE FLUTTER GIT\WAIE_MOBILE_FLUTTER\build\pay_android\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":path_provider_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\WAIE FLUTTER GIT\WAIE_MOBILE_FLUTTER\build\path_provider_android\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":package_info_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\WAIE FLUTTER GIT\WAIE_MOBILE_FLUTTER\build\package_info_plus\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":open_file_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\WAIE FLUTTER GIT\WAIE_MOBILE_FLUTTER\build\open_file_android\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":maps_launcher" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\WAIE FLUTTER GIT\WAIE_MOBILE_FLUTTER\build\maps_launcher\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":libphonenumber_plugin" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\WAIE FLUTTER GIT\WAIE_MOBILE_FLUTTER\build\libphonenumber_plugin\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":in_app_review" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\WAIE FLUTTER GIT\WAIE_MOBILE_FLUTTER\build\in_app_review\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":google_map_dynamic_key" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\WAIE FLUTTER GIT\WAIE_MOBILE_FLUTTER\build\google_map_dynamic_key\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":geolocator_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\WAIE FLUTTER GIT\WAIE_MOBILE_FLUTTER\build\geolocator_android\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":fluttertoast" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\WAIE FLUTTER GIT\WAIE_MOBILE_FLUTTER\build\fluttertoast\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":flutter_plugin_android_lifecycle" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\WAIE FLUTTER GIT\WAIE_MOBILE_FLUTTER\build\flutter_plugin_android_lifecycle\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":local_auth_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\WAIE FLUTTER GIT\WAIE_MOBILE_FLUTTER\build\local_auth_android\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":image_picker_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\WAIE FLUTTER GIT\WAIE_MOBILE_FLUTTER\build\image_picker_android\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":google_maps_flutter_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\WAIE FLUTTER GIT\WAIE_MOBILE_FLUTTER\build\google_maps_flutter_android\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":flutter_local_notifications" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\WAIE FLUTTER GIT\WAIE_MOBILE_FLUTTER\build\flutter_local_notifications\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":firebase_core" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\WAIE FLUTTER GIT\WAIE_MOBILE_FLUTTER\build\firebase_core\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":firebase_messaging" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\WAIE FLUTTER GIT\WAIE_MOBILE_FLUTTER\build\firebase_messaging\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":file_picker" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\WAIE FLUTTER GIT\WAIE_MOBILE_FLUTTER\build\file_picker\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":device_info_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\WAIE FLUTTER GIT\WAIE_MOBILE_FLUTTER\build\device_info_plus\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":connectivity_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\WAIE FLUTTER GIT\WAIE_MOBILE_FLUTTER\build\connectivity_plus\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":audio_session" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\WAIE FLUTTER GIT\WAIE_MOBILE_FLUTTER\build\audio_session\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":app_settings" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\WAIE FLUTTER GIT\WAIE_MOBILE_FLUTTER\build\app_settings\intermediates\library_assets\debug\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\WAIE FLUTTER GIT\WAIE_MOBILE_FLUTTER\android\app\src\main\assets"/><source path="D:\WAIE FLUTTER GIT\WAIE_MOBILE_FLUTTER\build\app\intermediates\shader_assets\debug\out"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\WAIE FLUTTER GIT\WAIE_MOBILE_FLUTTER\android\app\src\debug\assets"/></dataSet></merger>