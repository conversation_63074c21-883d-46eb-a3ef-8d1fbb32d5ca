// ignore_for_file: prefer_const_constructors, must_be_immutable

import 'dart:convert';
import 'dart:developer';

import 'package:chips_choice/chips_choice.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:waie_app/core/controller/menu_controller/fleet_structure_controller/fleet_controller.dart';
import 'package:waie_app/core/controller/vehicle_controller/add_digital_coupon_vehicle_controller.dart/add_digital_coupon_vehicle_controller.dart';
import 'package:waie_app/core/controller/vehicle_controller/add_digital_coupon_vehicle_controller.dart/add_digital_coupon_vehicle_details_controller.dart';
import 'package:waie_app/core/controller/vehicle_controller/vehicle_controller.dart';
import 'package:waie_app/models/division.dart';
import 'package:waie_app/utils/colors.dart';
import 'package:waie_app/utils/constants.dart';
import 'package:waie_app/utils/images.dart';
import 'package:waie_app/utils/insert_dictionary.dart';
import 'package:waie_app/utils/text_style.dart';
import 'package:waie_app/view/screen/menu_screen/user_management_screen/new_user_screen.dart';
import 'package:waie_app/view/widget/common_space_divider_widget.dart';
import 'package:waie_app/view/widget/common_text_field.dart';
import 'package:waie_app/view/widget/icon_and_image.dart';

import '../../../../core/controller/menu_controller/fleet_structure_controller/edit_vehicle_details_controller.dart';
import '../../../../core/controller/menu_controller/fleet_structure_controller/overview_controller.dart';
import '../../../../core/controller/menu_controller/fleet_structure_controller/view_vehicle_details_controller.dart';
import '../../../../core/controller/vehicle_controller/add_pure_dc_vehicle_controller.dart';
import '../../../../utils/validator.dart';
import '../../../widget/common_appbar_widget.dart';
import '../../../widget/common_button.dart';

class Location {
  final String city;
  final List<String> sites;
  Location({required this.city, required this.sites});
}

class AddDigitalCouponVehicleDetailsScreen extends StatefulWidget {
  const AddDigitalCouponVehicleDetailsScreen({super.key});

  @override
  State<AddDigitalCouponVehicleDetailsScreen> createState() =>
      _AddDigitalCouponVehicleDetailsScreenState();
}

class _AddDigitalCouponVehicleDetailsScreenState
    extends State<AddDigitalCouponVehicleDetailsScreen> {
  // AddDigitalCouponVehicleController addDigitalCouponVehicleController =
  //     Get.put(AddDigitalCouponVehicleController());

  AddDigitalCouponVehicleDetailsController
      addDigitalCouponVehicleDetailsController =
      Get.put(AddDigitalCouponVehicleDetailsController());

  @override
  Widget build(BuildContext context) {
    log("cust data ${addDigitalCouponVehicleDetailsController.customerData}");
    return WillPopScope(
      onWillPop: () async => false,
      child: GestureDetector(
        onTap: () {
          FocusScope.of(context).requestFocus(FocusNode());
        },
        child: Scaffold(
          backgroundColor: AppColor.cBackGround,
          body: SafeArea(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                simpleMyAppBar(
                    title: "",
                    onTap: () {
                      // addDigitalCouponVehicleDetailsController.country
                      //     .refresh();
                      addDigitalCouponVehicleDetailsController.vehlicType
                          .refresh();
                      addDigitalCouponVehicleDetailsController.plateNoController
                          .clear();
                      addDigitalCouponVehicleDetailsController
                          .driverNameController
                          .clear();
                      addDigitalCouponVehicleDetailsController.vehicleType
                          .refresh();
                      addDigitalCouponVehicleDetailsController.fuelType
                          .refresh();
                      addDigitalCouponVehicleDetailsController.offlineController
                          .clear();
                      addDigitalCouponVehicleDetailsController.quotaType
                          .refresh();
                      addDigitalCouponVehicleDetailsController.quotaClass
                          .refresh();
                      addDigitalCouponVehicleDetailsController.quotaValue
                          .refresh();
                      Get.back();
                      // addDigitalCouponVehicleController.tagLists.clear();
                      addDigitalCouponVehicleDetailsController.vehicleDetails
                          .clear();
                      addDigitalCouponVehicleDetailsController.countryList
                          .clear();
                      addDigitalCouponVehicleDetailsController.vehlicTypeList
                          .clear();
                      addDigitalCouponVehicleDetailsController.vehicleTypeList
                          .clear();
                      addDigitalCouponVehicleDetailsController.fuelList.clear();
                      addDigitalCouponVehicleDetailsController.quotaTypeList
                          .clear();
                      addDigitalCouponVehicleDetailsController.quotaClassList
                          .clear();
                    },
                    backString: "Back".trr),
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Obx(() {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            vehicleDetails(),
                            organization(),
                            verticalSpace(6),
                            refuelLimits(),
                            fillingDays(),
                            availableLocations()
                          ],
                        );
                      }),
                    ),
                  ),
                ),
              ],
            ),
          ),
          bottomNavigationBar: Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(color: AppColor.cLightGrey),
            child: Row(
              children: [
                Expanded(
                  child: CommonButton(
                    title: 'Cancel'.trr,
                    onPressed: () {
                      // addDigitalCouponVehicleDetailsController.country
                      //     .refresh();
                      addDigitalCouponVehicleDetailsController.vehlicType
                          .refresh();
                      addDigitalCouponVehicleDetailsController.plateNoController
                          .clear();
                      addDigitalCouponVehicleDetailsController
                          .driverNameController
                          .clear();
                      addDigitalCouponVehicleDetailsController.vehicleType
                          .refresh();
                      addDigitalCouponVehicleDetailsController.fuelType
                          .refresh();
                      addDigitalCouponVehicleDetailsController.offlineController
                          .clear();
                      addDigitalCouponVehicleDetailsController.quotaType
                          .refresh();
                      addDigitalCouponVehicleDetailsController.quotaClass
                          .refresh();
                      addDigitalCouponVehicleDetailsController.quotaValue
                          .refresh();
                      //addDigitalCouponVehicleController.tagLists.clear();
                      addDigitalCouponVehicleDetailsController.vehicleDetails
                          .clear();
                      addDigitalCouponVehicleDetailsController.countryList
                          .clear();
                      addDigitalCouponVehicleDetailsController.vehlicTypeList
                          .clear();
                      addDigitalCouponVehicleDetailsController.vehicleTypeList
                          .clear();
                      addDigitalCouponVehicleDetailsController.fuelList.clear();
                      addDigitalCouponVehicleDetailsController.quotaTypeList
                          .clear();
                      addDigitalCouponVehicleDetailsController.quotaClassList
                          .clear();
                      Get.back();
                    },
                    textColor: AppColor.cText,
                    btnColor: AppColor.cBackGround,
                  ),
                ),
                horizontalSpace(16),
                Expanded(
                  child: CommonButton(
                    title: 'Save'.trr,
                    onPressed: () {
                      // addDigitalCouponVehicleDetailsController.addFleet(
                      //     custId: addDigitalCouponVehicleDetailsController
                      //         .customerData['CUSTID'],
                      //     custUserName: addDigitalCouponVehicleDetailsController
                      //         .customerData['EMAILID'],
                      //     country: addDigitalCouponVehicleDetailsController.country,
                      //     vehlicType:  addDigitalCouponVehicleDetailsController
                      //       .vehlicType.value,
                      //     plateNo:  addDigitalCouponVehicleDetailsController
                      //       .plateNoController.text,
                      //     vehicleType:  addDigitalCouponVehicleDetailsController
                      //       .vehicleType.value,
                      //     fuelType:  addDigitalCouponVehicleDetailsController
                      //       .fuelType.value,
                      //     driverName: addDigitalCouponVehicleDetailsController
                      //       .driverNameController.text,
                      //     password: addDigitalCouponVehicleDetailsController
                      //       .passwordController.text,
                      //     refName: addDigitalCouponVehicleDetailsController
                      //       .vehicleReferenceController.text,
                      //     divisionCode: divisionCode,
                      //     divisionName: divisionName,
                      //     branchCode: branchCode,
                      //     branchName: branchName,
                      //     departmentCode: departmentCode,
                      //     departmentName: departmentName,
                      //     operationCode: operationCode,
                      //     operationName: operationName,
                      //     qoutaType: qoutaType,
                      //     qoutaClass: qoutaClass,
                      //     isSticker: isSticker,
                      //     mobileNumber: mobileNumber,
                      //     dcFuelQouta: dcFuelQouta,
                      //     fillingDaysList: fillingDaysList,
                      //     waieStationsList: waieStationsList,
                      //     nonWaieStationsList: nonWaieStationsList,
                      //     subContractorsStationsList:
                      //         subContractorsStationsList);

                    },
                    textColor: AppColor.cWhiteFont,
                    btnColor: AppColor.themeOrangeColor,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  organization() {
    return Column(children: [
      horizontalDivider(),
      addDcTitleRowWidget(
        title: "Organization".trr,
        isSelected:
            addDigitalCouponVehicleDetailsController.isOrganization.value,
        onTap: () {
          addDigitalCouponVehicleDetailsController.isOrganization.value =
              !addDigitalCouponVehicleDetailsController.isOrganization.value;
        },
      ),
      verticalSpace(16),
      addDigitalCouponVehicleDetailsController.isOrganization.value
          ? Padding(
              padding: const EdgeInsets.only(right: 16, left: 16),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "Division".trr,
                              style: pRegular14,
                            ),
                            Gap(5),
                            DropdownButtonFormField<DivisionModel>(
  value: addDigitalCouponVehicleDetailsController.selectedDivsion.value,
  items: addDigitalCouponVehicleDetailsController.divisionList
      .map((division) {
    return DropdownMenuItem<DivisionModel>(
      value: division, // Pass the whole object
      child: Text(division.typedesc),
    );
  }).toList(),
  onChanged: (division) async {
    if (division != null) {
      addDigitalCouponVehicleDetailsController.selectedDivsion.value = division;
      log("division typecode onChange ${division.typecode}"); // Now you can access typecode directly

      log(addDigitalCouponVehicleDetailsController.selectedDivsion.value?.typecode ?? '');
log(addDigitalCouponVehicleDetailsController.selectedDivsion.value?.typedesc ?? '');

      
      await addDigitalCouponVehicleDetailsController.clearSelectionsBelow('division');
      await addDigitalCouponVehicleDetailsController.loadBranch(division.typecode);
    }
  },
  style: pRegular14.copyWith(color: AppColor.cLabel),
  borderRadius: BorderRadius.circular(6),
  decoration: InputDecoration(
    hintText: 'Division'.trr,
    contentPadding: const EdgeInsets.only(left: 16, right: 16),
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(6),
    ),
  ),
  dropdownColor: AppColor.cLightGrey,
  icon: assetSvdImageWidget(image: DefaultImages.dropDownIcn),
)

                            // SizedBox(
                            //   width: 150,
                            //   child: Obx(() => DropdownButtonFormField(
                            //         value: addDigitalCouponVehicleDetailsController
                            //                 .selectedDivsion.value.isEmpty
                            //             ? null
                            //             : addDigitalCouponVehicleDetailsController
                            //                 .selectedDivsion.value,
                            //         items:
                            //             addDigitalCouponVehicleDetailsController
                            //                 .divisionList
                            //                 .map((division) {
                            //           return DropdownMenuItem<String>(
                            //             value: division,
                            //             child: Text(division.typedesc),
                            //           );
                            //         }).toList(),
                            //         onChanged: (division) async {
                            //           if (division != null)
                            //             addDigitalCouponVehicleDetailsController
                            //                 .selectedDivsion.value = division;
                            //           log("division typecode onChange ${division}"); 
                            //           log("divsion typedesc oncahnge ${addDigitalCouponVehicleDetailsController
                            //                 .selectedDivsion.value}");
                                      
                                      
                            //           await addDigitalCouponVehicleDetailsController
                            //               .clearSelectionsBelow('division');

                            //           await addDigitalCouponVehicleDetailsController
                            //               .loadBranch(division);
                            //         },
                            //         style: pRegular14.copyWith(
                            //             color: AppColor.cLabel),
                            //         borderRadius: BorderRadius.circular(6),
                            //         decoration: InputDecoration(
                            //           hintText: 'Division'.trr,
                            //           contentPadding: const EdgeInsets.only(
                            //               left: 16, right: 16),
                            //           border: OutlineInputBorder(
                            //               borderRadius:
                            //                   BorderRadius.circular(6)),
                            //         ),
                            //         dropdownColor: AppColor.cLightGrey,
                            //         icon: assetSvdImageWidget(
                            //             image: DefaultImages.dropDownIcn),
                            //       )),
                            // ),
                         
                          ],
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "Branch".trr,
                              style: pRegular14,
                            ),
                            Gap(5),
                            SizedBox(
                              width: 170,
                              child: Obx(() => DropdownButtonFormField<String>(
                                    value: addDigitalCouponVehicleDetailsController
                                            .selectedBranch.value.isEmpty
                                        ? null
                                        : addDigitalCouponVehicleDetailsController
                                            .selectedBranch.value,
                                    items:
                                        addDigitalCouponVehicleDetailsController
                                            .branchList
                                            .map((branch) {
                                      return DropdownMenuItem<String>(
                                          value: branch.typecode,
                                          child: Row(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,

                                            children: [
                                              Container(
                                                width: 80,
                                                height: 60,
                                                child: Text(
                                                  branch.typecode,
                                                  style: pMedium12.copyWith(
                                                      fontWeight:
                                                          FontWeight.bold),
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                              ),
                                              SizedBox(width: 12),
                                              Expanded(
                                                child: Text(
                                                  branch.typedesc,
                                                  style: pMedium12,
                                                  softWrap: true,
                                                  maxLines: 4,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                              ),
                                            ],
                                            //  SizedBox(
                                            //   width: 100,
                                            //   child: Text(
                                            //     branch.typedesc,
                                            //     style:
                                            //         pMedium12,
                                            //     maxLines:
                                            //         5,
                                            //     overflow:
                                            //         TextOverflow
                                            //             .ellipsis,
                                            //     softWrap: true,
                                            //   ),
                                            // ),
                                          ));
                                    }).toList(),
                                    selectedItemBuilder: (context) {
                                      return addDigitalCouponVehicleDetailsController
                                          .branchList
                                          .map((branch) {
                                        return Container(
                                          //padding: const EdgeInsets.only(right: 36, left: 8),
                                          alignment: Alignment.centerLeft,
                                          width: 100,

                                          child: Text(
                                            branch.typedesc,
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                            softWrap: true,
                                            style: pRegular14.copyWith(
                                                color: AppColor.cLabel),
                                          ),
                                        );
                                      }).toList();
                                    },
                                    onChanged: (value) async {
                                      if (value != null) {
                                        addDigitalCouponVehicleDetailsController
                                            .selectedBranch.value = value;
                                        await addDigitalCouponVehicleDetailsController
                                            .clearSelectionsBelow('branch');
                                        await addDigitalCouponVehicleDetailsController
                                            .loadDepartment(value);
                                      }
                                    },
                                    style: pRegular14.copyWith(
                                        color: AppColor.cLabel),
                                    decoration: InputDecoration(
                                      hintText: 'Branch'.trr,
                                      contentPadding: const EdgeInsets.only(
                                          left: 16, right: 16),
                                      border: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(6)),
                                    ),
                                    dropdownColor: AppColor.cLightGrey,
                                    icon: assetSvdImageWidget(
                                        image: DefaultImages.dropDownIcn),
                                  )),
                            )
                          ],
                        ),
                      ],
                    ),
                    verticalSpace(16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "Department".trr,
                              style: pRegular14,
                            ),
                            Gap(5),
                            SizedBox(
                              width: 170,
                              child: Obx(() => DropdownButtonFormField<String>(
                                    value: addDigitalCouponVehicleDetailsController
                                            .selectedDepartment.value.isEmpty
                                        ? null
                                        : addDigitalCouponVehicleDetailsController
                                            .selectedDepartment.value,
                                    items:
                                        addDigitalCouponVehicleDetailsController
                                            .departmentList
                                            .map((department) {
                                      return DropdownMenuItem<String>(
                                          value: department.typecode,
                                          child: Row(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Container(
                                                width: 80,
                                                height: 80,
                                                child: Text(
                                                  department.typecode,
                                                  style: pMedium12.copyWith(
                                                      fontWeight:
                                                          FontWeight.bold),
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                              ),
                                              SizedBox(width: 12),
                                              Expanded(
                                                child: Text(
                                                  department.typedesc,
                                                  style: pMedium12,
                                                  softWrap: true,
                                                  maxLines: 4,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                              ),
                                            ],
                                          ));
                                    }).toList(),
                                    selectedItemBuilder: (context) {
                                      return addDigitalCouponVehicleDetailsController
                                          .departmentList
                                          .map((department) {
                                        return Container(
                                          //padding: const EdgeInsets.only(right: 36, left: 8),
                                          alignment: Alignment.centerLeft,
                                          width: 100,

                                          child: Text(
                                            department.typedesc,
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                            softWrap: true,
                                            style: pRegular14.copyWith(
                                                color: AppColor.cLabel),
                                          ),
                                        );
                                      }).toList();
                                    },
                                    onChanged: (value) async {
                                      if (value != null) {
                                        addDigitalCouponVehicleDetailsController
                                            .selectedDepartment.value = value;
                                        await addDigitalCouponVehicleDetailsController
                                            .clearSelectionsBelow('department');
                                        await addDigitalCouponVehicleDetailsController
                                            .loadOperation(value);
                                      }
                                    },
                                    style: pRegular14.copyWith(
                                        color: AppColor.cLabel),
                                    decoration: InputDecoration(
                                      hintText: 'Department'.trr,
                                      contentPadding: const EdgeInsets.only(
                                          left: 16, right: 16),
                                      border: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(6)),
                                    ),
                                    dropdownColor: AppColor.cLightGrey,
                                    icon: assetSvdImageWidget(
                                        image: DefaultImages.dropDownIcn),
                                  )),
                            )
                          ],
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "Operation".trr,
                              style: pRegular14,
                            ),
                            Gap(5),
                            SizedBox(
                              width: 170,
                              child: Obx(() => DropdownButtonFormField<String>(
                                    value: addDigitalCouponVehicleDetailsController
                                            .selectedOperation.value.isEmpty
                                        ? null
                                        : addDigitalCouponVehicleDetailsController
                                            .selectedOperation.value,
                                    items:
                                        addDigitalCouponVehicleDetailsController
                                            .operationList
                                            .map((operation) {
                                      return DropdownMenuItem<String>(
                                          value: operation.typecode,
                                          child: Row(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Container(
                                                width: 80,
                                                height: 80,
                                                child: Text(
                                                  operation.typecode,
                                                  style: pMedium12.copyWith(
                                                      fontWeight:
                                                          FontWeight.bold),
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                              ),
                                              SizedBox(width: 12),
                                              Expanded(
                                                child: Text(
                                                  operation.typedesc,
                                                  style: pMedium12,
                                                  softWrap: true,
                                                  maxLines: 4,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                              ),
                                            ],
                                          ));
                                    }).toList(),
                                    selectedItemBuilder: (context) {
                                      return addDigitalCouponVehicleDetailsController
                                          .operationList
                                          .map((operation) {
                                        return Container(
                                          //padding: const EdgeInsets.only(right: 36, left: 8),
                                          alignment: Alignment.centerLeft,
                                          width: 100,

                                          child: Text(
                                            operation.typedesc,
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                            softWrap: true,
                                            style: pRegular14.copyWith(
                                                color: AppColor.cLabel),
                                          ),
                                        );
                                      }).toList();
                                    },
                                    onChanged: (value) async {
                                      if (value != null) {
                                        addDigitalCouponVehicleDetailsController
                                            .selectedOperation.value = value;
                                      }
                                    },
                                    style: pRegular14.copyWith(
                                        color: AppColor.cLabel),
                                    decoration: InputDecoration(
                                      hintText: 'Operation'.trr,
                                      contentPadding: const EdgeInsets.only(
                                          left: 16, right: 16),
                                      border: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(6)),
                                    ),
                                    dropdownColor: AppColor.cLightGrey,
                                    icon: assetSvdImageWidget(
                                        image: DefaultImages.dropDownIcn),
                                  )),
                            )
                          ],
                        ),
                      ],
                    ),
                  ]))
          : SizedBox(),
      verticalSpace(6),
    ]);
  }

  vehicleDetails() {
    return Column(
      children: [
        addDcTitleRowWidget(
          title: "Vehicle details".trr,
          isSelected:
              addDigitalCouponVehicleDetailsController.isVehicleDetail.value,
          onTap: () {
            addDigitalCouponVehicleDetailsController.isVehicleDetail.value =
                !addDigitalCouponVehicleDetailsController.isVehicleDetail.value;
          },
        ),
        verticalSpace(16),
        addDigitalCouponVehicleDetailsController.isVehicleDetail.value
            ? Container(
                padding: const EdgeInsets.only(right: 16, left: 16),
                decoration:
                    BoxDecoration(borderRadius: BorderRadius.circular(12)),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Country".trr,
                      style: pRegular13,
                    ),
                    verticalSpace(6),
                    DropdownButtonFormField(
                      value: addDigitalCouponVehicleDetailsController
                              .country.isEmpty
                          ? "KSA"
                          : addDigitalCouponVehicleDetailsController.country,
                      items: addDigitalCouponVehicleDetailsController
                          .countryList
                          .map((data) {
                        return DropdownMenuItem(
                          value: data["TYPECODE"],
                          child: Text(
                            data["TYPEDESC"].toString(),
                            style: pMedium12,
                            textAlign: TextAlign.center,
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        print(value);
                        addDigitalCouponVehicleDetailsController.country =
                            value.toString();
                        // PaginatedController.selectedDiv.value =
                        //     value.toString();
                        // reportController.loadBranch(value.toString());
                        //vehicleList.value = value.toString();
                      },
                      style: pRegular14.copyWith(color: AppColor.cLabel),
                      borderRadius: BorderRadius.circular(6),
                      dropdownColor: AppColor.cLightGrey,
                      icon:
                          assetSvdImageWidget(image: DefaultImages.dropDownIcn),
                      decoration: InputDecoration(
                        hintText: 'Country'.trr,
                        hintStyle:
                            pRegular14.copyWith(color: AppColor.cHintFont),
                        contentPadding:
                            const EdgeInsets.only(left: 16, right: 16),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(
                            color: AppColor.cBorder,
                          ),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(
                            color: AppColor.cBorder,
                          ),
                        ),
                        errorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(
                            color: AppColor.cBorder,
                          ),
                        ),
                        disabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(
                            color: AppColor.cBorder,
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(
                            color: AppColor.cBorder,
                          ),
                        ),
                      ),
                    ),
                    verticalSpace(16),
                    Text(
                      "Plate No".trr,
                      style: pRegular13,
                    ),
                    verticalSpace(6),
                    DropdownButtonFormField(
                      value: addDigitalCouponVehicleDetailsController
                              .vehlicType.value.isEmpty
                          ? null
                          : addDigitalCouponVehicleDetailsController
                              .vehlicType.value,
                      items: addDigitalCouponVehicleDetailsController
                          .vehlicTypeList
                          .map((data) {
                        return DropdownMenuItem(
                          value: data["TYPECODE"],
                          child: Text(
                            data["TYPEDESC"].toString(),
                            style: pMedium12,
                            textAlign: TextAlign.center,
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                         log("vehlicType $value");
                        addDigitalCouponVehicleDetailsController
                            .vehlicType.value = value.toString();
                        // PaginatedController.selectedDiv.value =
                        //     value.toString();
                        // reportController.loadBranch(value.toString());
                        //vehicleList.value = value.toString();
                      },
                      style: pRegular14.copyWith(color: AppColor.cLabel),
                      borderRadius: BorderRadius.circular(6),
                      dropdownColor: AppColor.cLightGrey,
                      icon:
                          assetSvdImageWidget(image: DefaultImages.dropDownIcn),
                      decoration: InputDecoration(
                        hintText: 'Type'.trr,
                        hintStyle:
                            pRegular14.copyWith(color: AppColor.cHintFont),
                        contentPadding:
                            const EdgeInsets.only(left: 16, right: 16),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(
                            color: AppColor.cBorder,
                          ),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(
                            color: AppColor.cBorder,
                          ),
                        ),
                        errorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(
                            color: AppColor.cBorder,
                          ),
                        ),
                        disabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(
                            color: AppColor.cBorder,
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(
                            color: AppColor.cBorder,
                          ),
                        ),
                      ),
                    ),
                    Gap(4),
                    cutomTextFormField(
                      controller: addDigitalCouponVehicleDetailsController
                          .plateNoController,
                      labelText: '',
                      hintText: 'Plate No'.trr,
                    ),
                    verticalSpace(16),
                    Text(
                      "Vehicle Type".trr,
                      style: pRegular13,
                    ),
                    verticalSpace(6),
                    DropdownButtonFormField(
                      value: addDigitalCouponVehicleDetailsController
                              .vehicleType.value.isEmpty
                          ? null
                          : addDigitalCouponVehicleDetailsController
                              .vehicleType.value,
                      items: addDigitalCouponVehicleDetailsController
                          .vehicleTypeList
                          .map((data) {
                        return DropdownMenuItem(
                          value: data["VEHICLETYPE"],
                          child: Text(
                            data["TYPEDESC"].toString(),
                            style: pMedium12,
                            textAlign: TextAlign.center,
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        log("vehicleType $value");
                        addDigitalCouponVehicleDetailsController
                            .vehicleType.value = value.toString();
                        // PaginatedController.selectedDiv.value =
                        //     value.toString();
                        // reportController.loadBranch(value.toString());
                        //vehicleList.value = value.toString();
                      },
                      style: pRegular14.copyWith(color: AppColor.cLabel),
                      borderRadius: BorderRadius.circular(6),
                      dropdownColor: AppColor.cLightGrey,
                      icon:
                          assetSvdImageWidget(image: DefaultImages.dropDownIcn),
                      decoration: InputDecoration(
                        hintText: 'Vehicle Type'.trr,
                        hintStyle:
                            pRegular14.copyWith(color: AppColor.cHintFont),
                        contentPadding:
                            const EdgeInsets.only(left: 16, right: 16),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(
                            color: AppColor.cBorder,
                          ),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(
                            color: AppColor.cBorder,
                          ),
                        ),
                        errorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(
                            color: AppColor.cBorder,
                          ),
                        ),
                        disabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(
                            color: AppColor.cBorder,
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(
                            color: AppColor.cBorder,
                          ),
                        ),
                      ),
                    ),
                    verticalSpace(16),
                    Text(
                      "Fuel".trr,
                      style: pRegular13,
                    ),
                    verticalSpace(6),
                    DropdownButtonFormField(
                      value: addDigitalCouponVehicleDetailsController
                              .fuelType.value.isEmpty
                          ? null
                          : addDigitalCouponVehicleDetailsController
                              .fuelType.value,
                      items: addDigitalCouponVehicleDetailsController.fuelList
                          .map((data) {
                        return DropdownMenuItem(
                          value: data["TYPECODE"],
                          child: Text(
                            data["TYPEDESC"].toString(),
                            style: pMedium12,
                            textAlign: TextAlign.center,
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        print(value);
                        addDigitalCouponVehicleDetailsController
                            .fuelType.value = value.toString();
                        // PaginatedController.selectedDiv.value =
                        //     value.toString();
                        // reportController.loadBranch(value.toString());
                        //vehicleList.value = value.toString();
                      },
                      style: pRegular14.copyWith(color: AppColor.cLabel),
                      borderRadius: BorderRadius.circular(6),
                      dropdownColor: AppColor.cLightGrey,
                      icon:
                          assetSvdImageWidget(image: DefaultImages.dropDownIcn),
                      decoration: InputDecoration(
                        hintText: 'Fuel'.trr,
                        hintStyle:
                            pRegular14.copyWith(color: AppColor.cHintFont),
                        contentPadding:
                            const EdgeInsets.only(left: 16, right: 16),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(
                            color: AppColor.cBorder,
                          ),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(
                            color: AppColor.cBorder,
                          ),
                        ),
                        errorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(
                            color: AppColor.cBorder,
                          ),
                        ),
                        disabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(
                            color: AppColor.cBorder,
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(
                            color: AppColor.cBorder,
                          ),
                        ),
                      ),
                    ),
                    verticalSpace(16),
                    cutomTextFormField(
                      controller: addDigitalCouponVehicleDetailsController
                          .driverNameController,
                      labelText: 'Driver Name'.trr,
                      hintText: 'Enter drivers name'.trr,
                    ),
                    verticalSpace(16),
                    cutomTextFormField(
                      controller: addDigitalCouponVehicleDetailsController
                          .passwordController,
                      labelText: 'Password'.trr,
                      hintText: 'Password'.trr,
                      keyboardType: TextInputType.number,
                      maxLength: 4,
                    ),
                    verticalSpace(16),
                    cutomTextFormField(
                      controller: addDigitalCouponVehicleDetailsController
                          .vehicleReferenceController,
                      labelText: 'Vehicle Reference Name'.trr,
                      hintText: 'Enter Reference Name'.trr,
                      maxLength: 20,
                    ),
                    verticalSpace(32)
                  ],
                ),
              )
            : SizedBox(),
      ],
    );
  }

  refuelLimits() {
    return Column(
      children: [
        horizontalDivider(),
        addDcTitleRowWidget(
          title: "Refuel limits".trr,
          isSelected:
              addDigitalCouponVehicleDetailsController.isRefuelLimits.value,
          onTap: () {
            addDigitalCouponVehicleDetailsController.isRefuelLimits.value =
                !addDigitalCouponVehicleDetailsController.isRefuelLimits.value;
          },
        ),
        verticalSpace(16),
        addDigitalCouponVehicleDetailsController.isRefuelLimits.value
            ? Padding(
                padding: const EdgeInsets.only(right: 16, left: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Quota type".trr,
                      style: pRegular13,
                    ),
                    verticalSpace(6),
                    DropdownButtonFormField(
                      value: addDigitalCouponVehicleDetailsController
                              .quotaType.value.isEmpty
                          ? null
                          : addDigitalCouponVehicleDetailsController
                              .quotaType.value,
                      items: addDigitalCouponVehicleDetailsController
                          .quotaTypeList
                          .map((data) {
                        return DropdownMenuItem(
                          value: data["TYPECODE"],
                          child: Text(
                            data["TYPEDESC"].toString(),
                            style: pMedium12,
                            textAlign: TextAlign.center,
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        print(value);
                        addDigitalCouponVehicleDetailsController
                            .quotaType.value = value.toString();
                        // PaginatedController.selectedDiv.value =
                        //     value.toString();
                        // reportController.loadBranch(value.toString());
                        //vehicleList.value = value.toString();
                      },
                      style: pRegular14.copyWith(color: AppColor.cLabel),
                      borderRadius: BorderRadius.circular(6),
                      dropdownColor: AppColor.cLightGrey,
                      icon:
                          assetSvdImageWidget(image: DefaultImages.dropDownIcn),
                      decoration: InputDecoration(
                        hintText: 'Quota type'.trr,
                        hintStyle:
                            pRegular14.copyWith(color: AppColor.cHintFont),
                        contentPadding:
                            const EdgeInsets.only(left: 16, right: 16),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(
                            color: AppColor.cBorder,
                          ),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(
                            color: AppColor.cBorder,
                          ),
                        ),
                        errorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(
                            color: AppColor.cBorder,
                          ),
                        ),
                        disabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(
                            color: AppColor.cBorder,
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(
                            color: AppColor.cBorder,
                          ),
                        ),
                      ),
                    ),
                    verticalSpace(16),
                    Text(
                      "Quota class".trr,
                      style: pRegular13,
                    ),
                    verticalSpace(6),
                    DropdownButtonFormField(
                      disabledHint: Text("LITER"),
                      value: addDigitalCouponVehicleDetailsController
                              .quotaClass.value.isEmpty
                          ? null
                          : addDigitalCouponVehicleDetailsController
                              .quotaClass.value,
                      items: addDigitalCouponVehicleDetailsController
                          .quotaClassList
                          .map((data) {
                        return DropdownMenuItem(
                          value: data.TYPECODE,
                          child: Text(
                            data.TYPEDESC,
                            style: pMedium12,
                            textAlign: TextAlign.center,
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        print(value);
                        addDigitalCouponVehicleDetailsController
                            .quotaClass.value = value.toString();
                        // PaginatedController.selectedDiv.value =
                        //     value.toString();
                        // reportController.loadBranch(value.toString());
                        //vehicleList.value = value.toString();
                      },
                      style: pRegular14.copyWith(color: AppColor.cLabel),
                      borderRadius: BorderRadius.circular(6),
                      dropdownColor: AppColor.cLightGrey,
                      icon:
                          assetSvdImageWidget(image: DefaultImages.dropDownIcn),
                      decoration: InputDecoration(
                        filled: true,
                        fillColor: AppColor.cLightGrey,
                        hintText: 'Quota class'.trr,
                        hintStyle:
                            pRegular14.copyWith(color: AppColor.cHintFont),
                        contentPadding:
                            const EdgeInsets.only(left: 16, right: 16),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(
                            color: AppColor.cBorder,
                          ),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(
                            color: AppColor.cBorder,
                          ),
                        ),
                        errorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(
                            color: AppColor.cBorder,
                          ),
                        ),
                        disabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(
                            color: AppColor.cBorder,
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(
                            color: AppColor.cBorder,
                          ),
                        ),
                      ),
                    ),
                    verticalSpace(10),
                    Row(
                      children: [
                        Row(
                          children: [
                            Text("Sticker".trr),
                            Checkbox(
                                value: addDigitalCouponVehicleDetailsController
                                    .sticker.value,
                                onChanged: (val) {
                                  addDigitalCouponVehicleDetailsController
                                      .sticker.value = val!;
                                }),
                          ],
                        ),
                        Row(
                          children: [
                            Text("Can use Digital Coupon (DC)".trr),
                            Checkbox(value: true, onChanged: (val) {}),
                          ],
                        ),
                      ],
                    ),
                    (addDigitalCouponVehicleDetailsController.sticker.value)
                        ? SizedBox()
                        : cutomTextFormField(
                            controller: addDigitalCouponVehicleDetailsController
                                .quotaValController,
                            labelText: "Mobile Number".trr,
                            hintText: '966XXXXXXXXX',
                            keyboardType: TextInputType.numberWithOptions(
                                signed: true, decimal: true)),
                    verticalSpace(16),
                    cutomTextFormField(
                        controller: addDigitalCouponVehicleDetailsController
                            .quotaValController,
                        labelText: "DC Fuel Quota (Liters)".trr,
                        hintText: '0',
                        keyboardType: TextInputType.numberWithOptions(
                            signed: true, decimal: true)),
                    verticalSpace(16),
                    cutomTextFormField(
                      readOnly: true,
                      controller: addDigitalCouponVehicleDetailsController
                          .quotaBalController,
                      labelText: "DC Fuel Balance (Liters)".trr,
                      filled: true,
                      fillColor: AppColor.cLightGrey,
                      hintText: '0',
                    ),
                    verticalSpace(32),
                    // if (Constants.isDCShow == 'Y')
                    //   addDigitalCouponVehicleDetailsController
                    //               .isDCActive.value ==
                    //           true
                    //       ? Padding(
                    //           padding: const EdgeInsets.symmetric(
                    //               horizontal: 16, vertical: 16),
                    //           child: Row(
                    //             children: [
                    //               SizedBox(
                    //                 height: 24,
                    //                 width: 24,
                    //                 child: Checkbox(
                    //                   value:
                    //                       addDigitalCouponVehicleDetailsController
                    //                                   .isDC.value ==
                    //                               'Y'
                    //                           ? addDigitalCouponVehicleController
                    //                               .isDigitalCoupon.value = true
                    //                           : addDigitalCouponVehicleController
                    //                               .isDigitalCoupon.value,
                    //                   onChanged:
                    //                       addDigitalCouponVehicleDetailsController
                    //                                   .isDC.value ==
                    //                               'Y'
                    //                           ? null // Disables the checkbox when isDCActive is true
                    //                           : (value) {
                    //                               addDigitalCouponVehicleController
                    //                                   .isDigitalCoupon
                    //                                   .value = value!;
                    //                               if (value == false) {
                    //                                 addDigitalCouponVehicleController
                    //                                     .isSticker
                    //                                     .value = false;
                    //                               } else {
                    //                                 if (addDigitalCouponVehicleController
                    //                                         .isSticker.value ==
                    //                                     true) {
                    //                                   addDigitalCouponVehicleController
                    //                                       .coupType.value = "S";
                    //                                 } else {
                    //                                   addDigitalCouponVehicleController
                    //                                       .coupType.value = "Q";
                    //                                 }
                    //                               }
                    //                             },
                    //                   shape: RoundedRectangleBorder(
                    //                       borderRadius:
                    //                           BorderRadius.circular(6)),
                    //                   activeColor: AppColor.themeDarkBlueColor,
                    //                 ),
                    //               ),
                    //               horizontalSpace(6),
                    //               Text(
                    //                 "Can use Digital Coupon (DC)".trr,
                    //                 style: pRegular13,
                    //               )
                    //             ],
                    //           ),
                    //         )
                    //       : SizedBox(),
                    // addDigitalCouponVehicleController.isDigitalCoupon.value ==
                    //         true
                    //     ? Column(
                    //         children: [
                    //           Padding(
                    //             padding: const EdgeInsets.symmetric(
                    //                 horizontal: 16, vertical: 16),
                    //             child: Row(
                    //               children: [
                    //                 SizedBox(
                    //                   height: 24,
                    //                   width: 24,
                    //                   child: Checkbox(
                    //                     value: addDigitalCouponVehicleDetailsController
                    //                                     .isCoup.value ==
                    //                                 "S" ||
                    //                             addDigitalCouponVehicleDetailsController
                    //                                     .isCoup.value ==
                    //                                 "B"
                    //                         ? addDigitalCouponVehicleController
                    //                             .isSticker.value = true
                    //                         : addDigitalCouponVehicleController
                    //                             .isSticker.value,
                    //                     onChanged:
                    //                         addDigitalCouponVehicleDetailsController
                    //                                     .isDC.value ==
                    //                                 'Y'
                    //                             ? null
                    //                             : (value) {
                    //                                 addDigitalCouponVehicleController
                    //                                     .isSticker
                    //                                     .value = value!;
                    //                                 print(
                    //                                     "isSticker>>>>>>> ${value}");
                    //                                 print(
                    //                                     "addDigitalCouponVehicleDetailsController.isCoup.value>>>>>>> ${addDigitalCouponVehicleDetailsController.isCoup.value}");
                    //                                 print(
                    //                                     "addDigitalCouponVehicleDetailsController.isCoup.value>>>>>>> ${addDigitalCouponVehicleDetailsController.isCoup}");
                    //                                 addDigitalCouponVehicleDetailsController
                    //                                         .coupType.value =
                    //                                     value == true
                    //                                         ? "S"
                    //                                         : "Q";
                    //                               },
                    //                     shape: RoundedRectangleBorder(
                    //                         borderRadius:
                    //                             BorderRadius.circular(6)),
                    //                     activeColor:
                    //                         AppColor.themeDarkBlueColor,
                    //                   ),
                    //                 ),
                    //                 horizontalSpace(6),
                    //                 Text(
                    //                   "Sticker".tr,
                    //                   style: pRegular13,
                    //                 )
                    //               ],
                    //             ),
                    //           ),
                    //         ],
                    //       )
                    //     : SizedBox()
                  ],
                ),
              )
            : SizedBox(),
      ],
    );
  }

//new
  Widget fillingDays() {
    final c = addDigitalCouponVehicleDetailsController;

    return Column(
      children: [
        horizontalDivider(),
        addDcTitleRowWidget(
          title: "Filling days".trr,
          isSelected: c.isFillingDays.value,
          onTap: () => c.isFillingDays.value = !c.isFillingDays.value,
        ),
        verticalSpace(16),
        Obx(() => c.isFillingDays.value
            ? Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Checkbox(
                        value: c.isAllDays.value,
                        onChanged: (checked) {
                          c.onSelectAllDays(checked ?? false);
                        },
                      ),
                      Text("Select All Days",
                          style: TextStyle(fontWeight: FontWeight.bold)),
                    ],
                  ),
                  ChipsChoice<int>.multiple(
                    value: c.tag.value,
                    onChanged: (val) => c.onDaysChanged(val),
                    choiceItems: C2Choice.listFrom<int, String>(
                      source: c.daysList,
                      value: (i, v) => i,
                      label: (i, v) => v,
                    ),
                    choiceBuilder: (item, i) => customChip(
                      label: item.label.toString().trr,
                      selected: item.selected,
                      onSelect: item.select!,
                    ),
                    wrapped: true,
                  ),
                  verticalSpace(32),
                  // Debug info or output:
                  Text("Selected Days: ${c.tags.value}",
                      style: TextStyle(fontSize: 12, color: Colors.grey)),
                ],
              )
            : SizedBox()),
      ],
    );
  }

//old
  // fillingDays() {
  //   return Column(
  //     children: [
  //       horizontalDivider(),
  //       addDcTitleRowWidget(
  //         title: "Filling days".trr,
  //         isSelected: addDigitalCouponVehicleController.isFillingDays.value,
  //         onTap: () {
  //           addDigitalCouponVehicleController.isFillingDays.value =
  //               !addDigitalCouponVehicleController.isFillingDays.value;
  //         },
  //       ),
  //       verticalSpace(16),
  //       addDigitalCouponVehicleController.isFillingDays.value
  //           ? Column(
  //               crossAxisAlignment: CrossAxisAlignment.start,
  //               children: [
  //                 ChipsChoice<int>.multiple(
  //                   value: addDigitalCouponVehicleController.tag.value,
  //                   onChanged: (val) {
  //                     addDigitalCouponVehicleController.tagLists.clear();
  //                     addDigitalCouponVehicleController.tag.value = val;
  //                     log("addDigitalCouponVehicleController.tag.value ${addDigitalCouponVehicleController.tag.value}");
  //                     addDigitalCouponVehicleController.tags =
  //                         val.map((i) => i.toString()).join(",");
  //                   },
  //                   choiceItems: C2Choice.listFrom<int, String>(
  //                     source: addDigitalCouponVehicleController.daysList,
  //                     value: (i, v) => i,
  //                     label: (i, v) => v,
  //                   ),
  //                   choiceBuilder: (item, i) {
  //                     return customChip(
  //                       label: item.label.toString().trr,
  //                       selected: item.selected,
  //                       onSelect: item.select!,
  //                     );
  //                   },
  //                   wrapped: true,
  //                 ),
  //                 verticalSpace(32)
  //               ],
  //             )
  //           : SizedBox(),
  //     ],
  //   );
  // }

  availableLocations() {
    return Column(
      children: [
        horizontalDivider(),
        addDcTitleRowWidget(
          title: "Available Locations".trr,
          isSelected: addDigitalCouponVehicleDetailsController
              .isAvailableStations.value,
          onTap: () {
            addDigitalCouponVehicleDetailsController.isAvailableStations.value =
                !addDigitalCouponVehicleDetailsController
                    .isAvailableStations.value;
          },
        ),
        (addDigitalCouponVehicleDetailsController.isAvailableStations.value)
            ? Column(
                children: [
                  Row(
                    children: addDigitalCouponVehicleDetailsController.options
                        .map((option) {
                      return Expanded(
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Radio<String>(
                                value: option,
                                groupValue:
                                    addDigitalCouponVehicleDetailsController
                                        .selectedOption.value,
                                onChanged: (value) async {
                                  if (value == null) return;
                                  addDigitalCouponVehicleDetailsController
                                      .selectedOption.value = value;
                                  await addDigitalCouponVehicleDetailsController
                                      .loadStationsForSelectedOption();
                                }),
                            Expanded(
                              child: Text(
                                option,
                                softWrap: true,
                                style: TextStyle(fontSize: 14),
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                  ),
                  verticalSpace(16),
                  Card(
                    color: Colors.white,
                    elevation: 10,
                    margin: EdgeInsets.all(16),
                    child: Padding(
                      padding: EdgeInsets.all(14),
                      child: Obx(() {
                        final list = addDigitalCouponVehicleDetailsController
                            .stationList;
                        print('UI rebuild, stationList count: ${list.length}');
                        log("stationList hash in UI rebuild: ${list.hashCode}");
                        log('UI rebuild, stationList count: ${addDigitalCouponVehicleDetailsController.stationList.length}');
                        if (addDigitalCouponVehicleDetailsController
                            .isLoading.value) {
                          return Center(child: CircularProgressIndicator());
                        } else if (addDigitalCouponVehicleDetailsController
                            .stationList.isEmpty) {
                          return Center(child: Text("No stations found."));
                        } else {
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "Available Locations",
                                style: TextStyle(
                                    fontWeight: FontWeight.bold, fontSize: 20),
                              ),
                              SizedBox(height: 15),
                              CheckboxListTile(
                                tristate: true,
                                value: addDigitalCouponVehicleDetailsController
                                        .isAllChecked
                                    ? true
                                    : addDigitalCouponVehicleDetailsController
                                            .isIndeterminate
                                        ? null
                                        : false, // true/false/null
                                title: Text('All Stations (WAIE)',
                                    style:
                                        TextStyle(fontWeight: FontWeight.bold)),
                                controlAffinity:
                                    ListTileControlAffinity.leading,
                                onChanged: (val) =>
                                    addDigitalCouponVehicleDetailsController
                                        .toggleAll(val ?? false),
                              ),
                              Divider(),
                              Container(
                                constraints: BoxConstraints(maxHeight: 350),
                                child: ListView.builder(
                                  shrinkWrap: true,
                                  itemCount:
                                      addDigitalCouponVehicleDetailsController
                                          .stationList.length,
                                  itemBuilder: (context, cityIdx) {
                                    var city =
                                        addDigitalCouponVehicleDetailsController
                                            .stationList[cityIdx];
                                    return Obx(() => ExpansionTile(
                                          key: ValueKey(city['title']),
                                          initiallyExpanded:
                                              city['expanded'].value,
                                          onExpansionChanged: (_) =>
                                              addDigitalCouponVehicleDetailsController
                                                  .toggleExpansion(cityIdx),
                                          leading: Checkbox(
                                            tristate: true,
                                            value:
                                                addDigitalCouponVehicleDetailsController
                                                    .getCityCheckboxValue(
                                                        cityIdx),
                                            // value: addDigitalCouponVehicleController
                                            //     .getCityCheckboxValue(
                                            //         cityIdx),
                                            onChanged: (val) =>
                                                addDigitalCouponVehicleDetailsController
                                                    .toggleCity(
                                                        cityIdx, val ?? false),
                                          ),
                                          title: Text(city['title'],
                                              style: TextStyle(
                                                  fontWeight: FontWeight.w600)),
                                          children: [
                                            Padding(
                                              padding: const EdgeInsets.only(
                                                  left: 16.0),
                                              child: Column(
                                                children: List.generate(
                                                    (city['data'] as List)
                                                        .length, (stationIdx) {
                                                  var station =
                                                      city['data'][stationIdx];
                                                  return Obx(() =>
                                                      CheckboxListTile(
                                                        contentPadding:
                                                            EdgeInsets
                                                                .symmetric(
                                                                    horizontal:
                                                                        0),
                                                        value: (station['value']
                                                                as RxBool)
                                                            .value,
                                                        title: Text(
                                                            station['title'],
                                                            style: TextStyle(
                                                                fontSize: 14)),
                                                        controlAffinity:
                                                            ListTileControlAffinity
                                                                .leading,
                                                        onChanged: (val) =>
                                                            addDigitalCouponVehicleDetailsController
                                                                .toggleStation(
                                                                    cityIdx,
                                                                    stationIdx,
                                                                    val ??
                                                                        false),
                                                      ));
                                                }),
                                              ),
                                            ),
                                          ],
                                        ));
                                  },
                                ),
                              ),
                            ],
                          );
                        }
                      }),
                    ),
                  )
                ],
              )
            : SizedBox()
      ],
    );
  }

  Widget customChip({
    required final String label,
    required final bool selected,
    required final Function(bool selected) onSelect,
  }) {
    return GestureDetector(
      onTap: () => onSelect(!selected),
      child: Container(
        height: 44,
        padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color:
                selected ? AppColor.themeDarkBlueColor : AppColor.cLightGrey),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(label,
                style: pRegular14.copyWith(
                    color: selected ? AppColor.cWhiteFont : AppColor.cText)),
            horizontalSpace(8),
            assetSvdImageWidget(
                image:
                    selected ? DefaultImages.cancelIcn : DefaultImages.addIcn,
                colorFilter: ColorFilter.mode(
                    selected ? AppColor.cWhiteFont : AppColor.cText,
                    BlendMode.srcIn),
                height: 16,
                width: 16),
          ],
        ),
      ),
    );
  }

  String err = '';
  final FocusNode noteFocus = FocusNode();
  Widget cutomTextFormField(
      {labelText,
      prefixIcon,
      suffix,
      keyboardType,
      validator,
      onChanged,
      borderColor,
      focusBorderColor,
      filled,
      readOnly = false,
      fillColor,
      controller,
      obscureText = false,
      obscuringCharacter,
      maxLength,
      hintText,
      inputFormatters,
      initialValue,
      maxLines,
      onTap}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          labelText,
          style: pRegular12,
        ),
        Gap(5),
        TextFormField(
          focusNode: noteFocus,
          controller: controller,
          onTap: onTap,
          onChanged: onChanged,
          inputFormatters: inputFormatters,
          initialValue: initialValue,
          maxLines: maxLines ?? 1,
          validator: (v) {
            setState(() {
              err = validator!(v!);
            });
            print("======= $err");
            if (err == '') {
              return null;
            } else {
              noteFocus.requestFocus();
              return err;
            }
          },
          style: pRegular14.copyWith(color: AppColor.cLabel),
          readOnly: readOnly,
          maxLength: maxLength,
          keyboardType: keyboardType,
          obscureText: obscureText,
          cursorColor: AppColor.cHintFont,
          obscuringCharacter: obscuringCharacter ?? ' ',
          textInputAction: TextInputAction.done,
          decoration: InputDecoration(
            prefixIcon: prefixIcon,
            suffixIcon: suffix,
            suffixIconConstraints: BoxConstraints(maxWidth: 45, minWidth: 42),
            errorStyle: TextStyle(
              height: 0,
              fontSize: 0,
              decorationThickness: 0,
            ),
            counterText: '',
            counterStyle: TextStyle(fontSize: 0, height: 0),
            hintText: hintText,
            hintStyle: pRegular14.copyWith(color: AppColor.cHintFont),
            fillColor: fillColor ?? AppColor.cBackGround,
            filled: filled,
            border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: borderColor ?? AppColor.cBorder)),
            disabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: borderColor ?? AppColor.cBorder)),
            enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: borderColor ?? AppColor.cBorder)),
            errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide:
                    BorderSide(color: borderColor ?? AppColor.cRedText)),
            focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                    color: focusBorderColor ?? AppColor.cFocusedTextField)),
            contentPadding: EdgeInsets.only(
                left: 16, top: maxLength != null ? 0 : 16, right: 16),
          ),
        ),
      ],
    );
  }
}

Widget addDcTitleRowWidget(
    {required String title, required bool isSelected, Function()? onTap}) {
  return GestureDetector(
    onTap: onTap,
    child: Container(
      width: Get.width,
      color: AppColor.cBackGround,
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: pBold20,
          ),
          assetSvdImageWidget(
              image: isSelected == true
                  ? DefaultImages.arrowUpIcn
                  : DefaultImages.dropDownIcn,
              width: 24,
              height: 24)
        ],
      ),
    ),
  );
}
